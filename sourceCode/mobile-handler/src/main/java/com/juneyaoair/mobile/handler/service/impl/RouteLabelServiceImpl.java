package com.juneyaoair.mobile.handler.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.appenum.ChannelCodeEnum;
import com.juneyaoair.appenum.av.LabelTypeEnum;
import com.juneyaoair.appenum.flight.FlightDirection;
import com.juneyaoair.baseclass.av.common.FlightNoIcon;
import com.juneyaoair.baseclass.av.common.FlightSimpleInfo;
import com.juneyaoair.baseclass.av.common.LabelInfo;
import com.juneyaoair.baseclass.av.common.TransferInfo;
import com.juneyaoair.baseclass.basicsys.response.AirPortInfoDto;
import com.juneyaoair.baseclass.common.request.BasicBaseReq;
import com.juneyaoair.baseclass.common.response.BasicBaseResp;
import com.juneyaoair.baseclass.request.av.QueryFlightFareReq;
import com.juneyaoair.baseclass.response.av.FlightInfo;
import com.juneyaoair.baseclass.routelabel.RouteLabelDTO;
import com.juneyaoair.client.ClientInfo;
import com.juneyaoair.mobile.comm.service.IRedisService;
import com.juneyaoair.mobile.constants.MobileCoreBeanNames;
import com.juneyaoair.mobile.handler.HandlerConstants;
import com.juneyaoair.mobile.handler.comm.RedisKeyConfig;
import com.juneyaoair.mobile.handler.config.bean.HandConfig;
import com.juneyaoair.mobile.handler.service.LocalCacheService;
import com.juneyaoair.mobile.handler.service.RouteLabelService;
import com.juneyaoair.mobile.handler.service.bean.country.TCountryDTO;
import com.juneyaoair.thirdentity.response.detr.PtSegmentInfo;
import com.juneyaoair.utils.IPUtil;
import com.juneyaoair.utils.StringUtil;
import com.juneyaoair.utils.http.HttpResult;
import com.juneyaoair.utils.http.HttpUtil;
import com.juneyaoair.utils.json.JsonUtil;
import com.juneyaoair.utils.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RouteLabelServiceImpl implements RouteLabelService {

    private static final String DEFAULT = "default";

    @Autowired
    private HandConfig handConfig;

    @Autowired
    private LocalCacheService localCacheService;

    @Resource(name = MobileCoreBeanNames.API_REDIS_SERVICE)
    private IRedisService apiRedisService;

    /**
     * Redis缓存获取标签
     * @return
     */
    @Override
    public List<RouteLabelDTO> getCacheRouteLabel() {
        //先获取缓存，无缓存信息的再发送请求 key:RedisKeyConfig.COMMON_AIRLINE_LABEL
        String key = RedisKeyConfig.COMMON_AIRLINE_LABEL;
        String json = apiRedisService.getData(key);
        List<RouteLabelDTO> routeLabelDTOS;
        if (StringUtil.isNullOrEmpty(json) || "null".equals(json)) {
            routeLabelDTOS = fetchRouteLabel();
            // 缓存 2h
            json = new Gson().toJson(routeLabelDTOS);
            apiRedisService.putData(key, json, 120 * 60L);
        } else {
            Type type = new TypeToken<List<RouteLabelDTO>>() {
            }.getType();
            routeLabelDTOS = new Gson().fromJson(json, type);

        }
        return routeLabelDTOS;
    }


    /**
     * 获取航线标签
     *
     * @return
     */
    @Override
    public List<RouteLabelDTO> fetchRouteLabel() {

        //
        String url = HandlerConstants.BASIC_PROVIDER_URL + HandlerConstants.BASIC_ROUTE_LABEL;
        //

        BasicBaseReq basicBaseReq = new BasicBaseReq(HandlerConstants.BASIC_INFO_VERSION, ChannelCodeEnum.MOBILE.getChannelCode(), IPUtil.getLocalIp());
        HttpResult httpResult = HttpUtil.doPostClient(basicBaseReq, url, null, handConfig.getConnectTimeout(), handConfig.getReadTimeout());

        if (httpResult.isResult()) {
            BasicBaseResp<List<RouteLabelDTO>> resultDTO = (BasicBaseResp<List<RouteLabelDTO>>) JsonUtil.toDateSerializer(httpResult.getResponse(), new TypeToken<BasicBaseResp<List<RouteLabelDTO>>>() {
            }.getType());
            return resultDTO.getResult();
        } else {
            log.error("fetchRouteLabel error,httpResult:{}", httpResult);
            return new ArrayList<>();
        }
    }


    /**
     * 处理航线标签以匹配航班信息并更新航班标签。
     * @param flightInfo
     */
    @Override
    public void handByRouteLabel(FlightInfo flightInfo, ClientInfo clientInfo) {

        // 获取航线标签
        List<RouteLabelDTO> routeLabels = getCacheRouteLabel();

        // 过滤 航线标签层 启用、日期、渠道
        routeLabels = filterRouteLabelsByDateAndChannel(routeLabels, clientInfo.getHeadChannelCode());

        if (CollectionUtils.isEmpty(routeLabels)) {
            return;
        }

        // 初始化参数
        TransferDetails transferDetails = initializeTransferDetails(flightInfo);

        // 判断航线匹配
        List<RouteLabelDTO> matchingLabels = routeLabels.stream()
                .filter(label -> matchesAnyRule(label.getRuleDTOList(), flightInfo, transferDetails))
                .collect(Collectors.toList());

        // 根据渠道更新标签信息
        updateFlightInfoLabels(flightInfo, matchingLabels, clientInfo.getHeadChannelCode());
    }


    /**
     * 业务测 以国内行李直挂行为定义为‘通程航班’。故直接使用baggageThroughFlag判断
     *
     * @param flightInfo
     * @param fareReq
     */
    @Override
    public void setTransferLabel(FlightInfo flightInfo, QueryFlightFareReq fareReq) {
        List<LabelInfo> currentLabels = Optional.ofNullable(flightInfo.getLabelInfoList())
                .orElse(new ArrayList<>());

        if (flightInfo.getBaggageThroughFlag() != null) {
            boolean addBagLabel = isAddBagLabel(flightInfo);
            if (addBagLabel) {
                LabelInfo labelInfo = null;
                //国际航班定义为行李直挂
                if (HandlerConstants.TRIP_TYPE_I.equals(fareReq.getTripType())) {
                    List<LabelInfo> labelInfoList =  handConfig.getLabelInfo("transferBaggage");
                    if(CollectionUtils.isNotEmpty(labelInfoList)){
                        labelInfo = labelInfoList.get(0);
                    }
                } else {
                    // 国内转国内行李直挂标签限定为通程航班
                    List<LabelInfo> labelInfoList =  handConfig.getLabelInfo("throughFlight");
                    if(CollectionUtils.isNotEmpty(labelInfoList)){
                        labelInfo = labelInfoList.get(0);
                    }
                }
                if(labelInfo !=null){
                    currentLabels.add(labelInfo);
                }
            }
        }
        // 暂未设置联程值机标签
        flightInfo.setLabelInfoList(currentLabels);
    }


    /**
     * NULL、"1"、"0"、"1/0"、"1/1"、"0/0"、"0/1" e.g.
     * @param flightInfo
     * @return
     */
    private static boolean isAddBagLabel(FlightInfo flightInfo) {
        boolean addBagLabel = false;
        // 多段
        if (flightInfo.getBaggageThroughFlag().contains("/")) {
            // split 1/1 match FlightDirection G/B
            String[] split = flightInfo.getBaggageThroughFlag().split("/");
            if (FlightDirection.GO.getCode().equals(flightInfo.getFlightDirection())) {
                addBagLabel = "1".equals(split[0]);
            }
            if (FlightDirection.BACK.getCode().equals(flightInfo.getFlightDirection())) {
                addBagLabel = "1".equals(split[1]);
            }
        } else {
            addBagLabel = "1".equals(flightInfo.getBaggageThroughFlag());
        }
        return addBagLabel;
    }

    /**
     * 航线标签是否启用、标签开始结束日期、渠道
     *
     * @param routeLabels
     * @param channelCode
     * @return
     */
    private List<RouteLabelDTO> filterRouteLabelsByDateAndChannel(List<RouteLabelDTO> routeLabels, String channelCode ) {
        routeLabels = routeLabels.stream().filter(label -> {
            boolean enable = label.isEnableStatus();
            boolean inDateRange = DateUtil.isIn(new Date(), DateUtil.parse(label.getStartDate()), DateUtil.parse(label.getEndDate()));
            boolean inChannelRange = label.getChannelList().stream().anyMatch(channel -> channelCode.equals(channel.getChannel()));
            return enable && inChannelRange && inDateRange;
        }).collect(Collectors.toList());
        return routeLabels;
    }

    private TransferDetails initializeTransferDetails(FlightInfo flightInfo) {
        if (CollectionUtils.isEmpty(flightInfo.getTransferInfoList())) {
            return new TransferDetails();
        }

        if (flightInfo.getTransferInfoList().size() > 1) {
            return new TransferDetails();
        }

        TransferInfo transferInfo = flightInfo.getTransferInfoList().get(0);
        String transfer = transferInfo.getTransferAirPortFrom();
        String dateLimit = transferInfo.getCrossDays() > 0 ? "overnight" : "sameDay";
        boolean sameAirport = transferInfo.isSameAirport();
        long transferTime = transferInfo.getTransferTime();
        FlightSimpleInfo preFlight = transferInfo.getPreFlightInfo();
        FlightSimpleInfo nextFlight = transferInfo.getNextFlightInfo();

        return new TransferDetails(transfer, dateLimit, sameAirport, transferTime, preFlight, nextFlight);
    }


    private boolean matchesAnyRule(List<RouteLabelDTO.RouteLabelRuleDTO> rules, FlightInfo flightInfo, TransferDetails transferDetails) {
        return rules.stream()
                .filter(rule -> rule.isEnableStatus()) // 过滤启用的规则
                .anyMatch(rule -> matchesRule(rule, flightInfo, transferDetails));
    }

    private boolean matchesRule(RouteLabelDTO.RouteLabelRuleDTO rule, FlightInfo flightInfo, TransferDetails transferDetails) {
        return
                isTravelDateValid(rule, flightInfo.getFlightDate()) &&
                // CARRIER
                isCarrierMatching(rule, flightInfo) &&
                // DEP
                isDepartureRegionMatching(rule, flightInfo) &&
                isDepartureTerminalMatching(rule, flightInfo) &&
                // ARR
                isArrivalRegionMatching(rule, flightInfo) &&
                isArrivalTerminalMatching(rule, flightInfo) &&
                // TRANSIT - 验证是否中转字段
                isTransitMatching(rule, transferDetails) &&
                // TRANS
                isTransferAirportMatching(rule, transferDetails) &&
//                isTransferTerminalMatching(rule, transferDetails) &&
                isSameAirportRequirementMet(rule, transferDetails.sameAirport) &&
                isTransferTimeValid(rule, transferDetails) &&
                isDateLimitMatching(rule, transferDetails.dateLimit) &&
                isFlightCrossDayValid(rule, transferDetails) &&
                // 新增的适用航班号和适用机型验证
                isApplyFlightNoMatching(rule, flightInfo, transferDetails) &&
                isApplyAircraftTypeMatching(rule, flightInfo, transferDetails);
    }


    /**
     * 验证航班日期是否符合规则中定义的日期范围。
     *
     * @param rule 包含规则的对象，提供开始和结束日期。
     * @param flightDate 航班的实际日期。
     * @return 如果航班日期在规则的日期范围内，返回 true；否则返回 false。
     */
    private boolean isTravelDateValid(RouteLabelDTO.RouteLabelRuleDTO rule, String flightDate) {
        return DateUtils.compareDate(rule.getRouteStartDate(), rule.getRouteEndDate(), flightDate, DateUtils.YYYY_MM_DD_PATTERN);
    }


    /**
     * 判断航班承运人是否符合规则中的承运人要求。
     *
     * @param rule 包含规则的对象，提供承运人信息。
     * @param flightInfo 包含航班信息的对象，提供实际的承运人信息。
     * @return 如果航班的承运人信息匹配规则中的要求，返回 true；否则返回 false。
     */
    private boolean isCarrierMatching(RouteLabelDTO.RouteLabelRuleDTO rule, FlightInfo flightInfo) {
        if (StringUtils.isBlank(rule.getCarrier())) {
            return true;
        }

        List<String> carriers = new ArrayList<>(Arrays.asList(rule.getCarrier().split(",")));
        return carriers.stream().anyMatch(carriersStr -> {
            if (carriersStr.contains("-")) {
                // 组合
                String[] carrierArray = carriersStr.split("-");
                if (carrierArray.length != 2 || flightInfo.getCarrierFlightNoIconList().size() != 2) {
                    return false;
                }
                for (int i = 0; i < flightInfo.getCarrierFlightNoIconList().size(); i++) {
                    if (!flightInfo.getCarrierFlightNoIconList().get(i).getFlightNo().startsWith(carrierArray[i])) {
                        return false;
                    }
                }
                // allMatch
                return true;
            } else {
                // 单组
                return flightInfo.getCarrierFlightNoIconList().stream().allMatch(flightNoIcon -> flightNoIcon.getFlightNo().startsWith(rule.getCarrier()));
            }
        });

    }

    /**
     * 检查航班的出发地区是否符合规则中的要求。
     *
     * @param rule 包含规则的对象，提供出发机场、国家和地区的信息。
     * @param flightInfo 包含航班信息的对象，提供实际的出发机场、国家和地区的信息。
     * @return 如果航班的出发地区符合规则中的要求，返回 true；否则返回 false。
     */
    private boolean isDepartureRegionMatching(RouteLabelDTO.RouteLabelRuleDTO rule, FlightInfo flightInfo) {
        String depAirport = flightInfo.getDepAirport();
        String depCountry = flightInfo.getDeptCountryNo();
        TCountryDTO depCountryDto = getCountryDTO(depCountry);


        return (Strings.isNullOrEmpty(rule.getDepAirport()) || rule.getDepAirport().contains(depAirport))
                && (Strings.isNullOrEmpty(rule.getDepCountry()) || rule.getDepCountry().contains(depCountry))
                && (Strings.isNullOrEmpty(rule.getDepRegion()) || rule.getDepRegion().contains(depCountryDto == null ? "" : depCountryDto.getRegionCode()));
    }

    /**
     * 检查航班的出发航站楼是否符合规则中的要求。
     *
     * @param rule 包含规则的对象，提供出发航站楼的信息。
     * @param flightInfo 包含航班信息的对象，提供实际的出发航站楼信息。
     * @return 如果航班的出发航站楼符合规则中的要求，返回 true；否则返回 false。
     */
    private boolean isDepartureTerminalMatching(RouteLabelDTO.RouteLabelRuleDTO rule, FlightInfo flightInfo) {
        return Strings.isNullOrEmpty(rule.getDepTerminal()) || rule.getDepTerminal().contains(flightInfo.getDepTerm());
    }

    /**
     * 检查航班的到达地区是否符合规则中的要求。
     *
     * @param rule 包含规则的对象，提供到达机场、国家和地区的信息。
     * @param flightInfo 包含航班信息的对象，提供实际的到达机场、国家和地区的信息。
     * @return 如果航班的到达地区符合规则中的要求，返回 true；否则返回 false。
     */
    private boolean isArrivalRegionMatching(RouteLabelDTO.RouteLabelRuleDTO rule, FlightInfo flightInfo) {
        String arrAirport = flightInfo.getArrAirport();
        String arrCountry = flightInfo.getCountryNo();
        TCountryDTO arrCountryDto = getCountryDTO(arrCountry);

        return (Strings.isNullOrEmpty(rule.getArrAirport()) || rule.getArrAirport().contains(arrAirport))
                && (Strings.isNullOrEmpty(rule.getArrCountry()) || rule.getArrCountry().contains(arrCountry))
                && (Strings.isNullOrEmpty(rule.getArrRegion()) || rule.getArrRegion().contains(arrCountryDto == null ? "" : arrCountryDto.getRegionCode()));
    }

    /**
     * 检查航班的到达航站楼是否符合规则中的要求。
     *
     * @param rule 包含规则的对象，提供到达航站楼的信息。
     * @param flightInfo 包含航班信息的对象，提供实际的到达航站楼信息。
     * @return 如果航班的到达航站楼符合规则中的要求，返回 true；否则返回 false。
     */
    private boolean isArrivalTerminalMatching(RouteLabelDTO.RouteLabelRuleDTO rule, FlightInfo flightInfo) {
        return Strings.isNullOrEmpty(rule.getArrTerminal()) || rule.getArrTerminal().contains(flightInfo.getArrTerm());
    }

    private TCountryDTO getCountryDTO(String countryCode) {
        List<TCountryDTO> countryDTOList = localCacheService.getLocalCountry(Arrays.asList(countryCode));
        return countryDTOList.stream()
                .filter(dto -> dto.getCountryCode().equals(countryCode))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断是否中转字段是否符合规则中的要求。
     *
     * @param rule 包含规则的对象，提供是否中转的信息。
     * @param transferDetails 转机信息的对象，提供实际的中转状态。
     * @return 如果中转状态符合规则中的要求，返回 true；否则返回 false。
     */
    private boolean isTransitMatching(RouteLabelDTO.RouteLabelRuleDTO rule, TransferDetails transferDetails) {
        if (StringUtils.isBlank(rule.getTransit())) {
            return true; // 如果规则没有指定中转要求，则匹配
        }

        boolean isTransferFlight = !DEFAULT.equals(transferDetails.transfer);

        // "1" 表示中转航班，"0" 表示非中转航班
        if ("1".equals(rule.getTransit())) {
            return isTransferFlight; // 规则要求中转航班
        } else if ("0".equals(rule.getTransit())) {
            return !isTransferFlight; // 规则要求非中转航班
        }

        return true; // 其他情况默认匹配
    }

    /**
     * 判断中转航站楼是否符合规则中的要求。
     *
     * @param rule 包含规则的对象，提供中转航站楼的信息。
     * @param transferDetails 转机信息的对象，提供实际的中转航站楼信息。
     * @return 如果中转航站楼符合规则中的要求，返回 true；否则返回 false。
     */
//    private boolean isTransferTerminalMatching(RouteLabelDTO.RouteLabelRuleDTO rule, TransferDetails transferDetails) {
//        // 当前 TransferDetails 不包含中转航站楼信息，暂时跳过验证
//        // 需要扩展 TransferDetails 类来包含中转航站楼信息
//        return StringUtils.isBlank(rule.getTransDepTerminal());
//    }

    /**
     * 判断转机机场是否符合规则中的要求。
     *
     * @param rule 包含规则的对象，提供转机机场的信息。
     * @param transferDetails 转机信息的对象，提供实际的转机机场信息。
     * @return 如果转机机场符合规则中的要求，返回 true；否则返回 false。
     */
    private boolean isTransferAirportMatching(RouteLabelDTO.RouteLabelRuleDTO rule, TransferDetails transferDetails) {
        return StringUtils.isBlank(rule.getTransAirport()) || rule.getTransAirport().contains(transferDetails.transfer);
    }

    /**
     * 判断是否满足规则中对同机场转机的要求。
     *
     * @param rule 包含规则的对象，提供同机场转机的要求。
     * @param sameAirport 表示是否在同一机场转机。
     * @return 如果满足规则中的要求，返回 true；否则返回 false。
     */
    private boolean isSameAirportRequirementMet(RouteLabelDTO.RouteLabelRuleDTO rule, boolean sameAirport) {
        if (StringUtils.isBlank(rule.getTransSameAirport())) {
            return true;
        }
        if("A".equals(rule.getTransSameAirport())){
            return true;
        }
        return "Y".equals(rule.getTransSameAirport()) == sameAirport;
    }

    /**
     * 判断转机时间是否符合规则中的要求。
     *
     * @param rule 包含规则的对象，提供转机时间的范围。
     * @param transferDetails 转机信息的对象，提供实际的转机时间。
     * @return 如果转机时间在规则中定义的范围内，返回 true；否则返回 false。
     */
    private boolean isTransferTimeValid(RouteLabelDTO.RouteLabelRuleDTO rule, TransferDetails transferDetails) {
        if (StringUtils.isBlank(rule.getTransTime())) {
            return true;
        }
        String[] transferTimes = rule.getTransTime().split("-");
        long minTime = Long.parseLong(transferTimes[0]) * 60 * 1000;
        long maxTime = Long.parseLong(transferTimes[1]) * 60 * 1000;
        return transferDetails.transferTime >= minTime && transferDetails.transferTime <= maxTime;
    }

    /**
     * 判断日期限制是否符合规则中的要求。
     *
     * @param rule 包含规则的对象，提供日期限制的信息。
     * @param dateLimit 转机日期限制的实际值。
     * @return 如果日期限制符合规则中的要求，返回 true；否则返回 false。
     */
    private boolean isDateLimitMatching(RouteLabelDTO.RouteLabelRuleDTO rule, String dateLimit) {
        return StringUtils.isBlank(rule.getTransDateLimit()) || rule.getTransDateLimit().contains(dateLimit);
    }

    /**
     * 判断航班是否符合规则中定义的跨天限制。
     *
     * @param rule 包含规则的对象，提供跨天限制的信息。
     * @param transferDetails 转机信息的对象，提供实际的转机前后航班信息。
     * @return 如果航班符合跨天限制的要求，返回 true；否则返回 false。
     */
    private boolean isFlightCrossDayValid(RouteLabelDTO.RouteLabelRuleDTO rule, TransferDetails transferDetails) {
        if (rule.getTransPreFlightDateLimit() == null && rule.getTransNextFlightDateLimit() == null) {
            return true;
        }
        FlightSimpleInfo preFlight = transferDetails.preFlightSimpleInfo;
        FlightSimpleInfo nextFlight = transferDetails.nextFlightSimpleInfo;

        if (preFlight == null || nextFlight == null) {
            return false; // 如果没有前后航班信息，则不匹配跨天限制规则
        }

        boolean preFlightValid = rule.getTransPreFlightDateLimit() == null ||
                                preFlight.getDays() <= rule.getTransPreFlightDateLimit();
        boolean nextFlightValid = rule.getTransNextFlightDateLimit() == null ||
                                 nextFlight.getDays() <= rule.getTransNextFlightDateLimit();

        return preFlightValid && nextFlightValid;
    }

    private void updateFlightInfoLabels(FlightInfo flightInfo, List<RouteLabelDTO> matchingLabels,  String channelCode) {
        List<LabelInfo> currentLabels = Optional.ofNullable(flightInfo.getLabelInfoList())
                .orElse(new ArrayList<>());
        matchingLabels.forEach(label -> {
            LabelInfo labelInfo = new LabelInfo();
            labelInfo.setLabelName(label.getLabelName());
            labelInfo.setPictureUrl(label.getLabelImg());
            labelInfo.setDisplayAreaTitle(label.getDisplayAreaTitle());
            labelInfo.setDisplayMainText(label.getDisplayMainText());
            labelInfo.setDisplayViceText(label.getDisplayViceText());
            label.getChannelList().stream()
                    .filter(channel -> channelCode.equals(channel.getChannel()))
                    .findFirst()
                    .ifPresent(channelDTO -> labelInfo.setLabelUrl(channelDTO.getChannelUrl()));

            labelInfo.setLabelType(StringUtils.isBlank(label.getLabelDisplayType()) ? LabelTypeEnum.MAIN.getType() : label.getLabelDisplayType());
            currentLabels.add(labelInfo);
        });
        flightInfo.setLabelInfoList(currentLabels);
    }


    private static class TransferDetails {
        String transfer;
        String dateLimit;
        boolean sameAirport;
        long transferTime;
        FlightSimpleInfo preFlightSimpleInfo;
        FlightSimpleInfo nextFlightSimpleInfo;

        TransferDetails() {
            this(DEFAULT, DEFAULT, false, 0, null, null);
        }

        TransferDetails(String transfer, String dateLimit, boolean sameAirport, long transferTime,
                        FlightSimpleInfo preFlightSimpleInfo, FlightSimpleInfo nextFlightSimpleInfo) {
            this.transfer = transfer;
            this.dateLimit = dateLimit;
            this.sameAirport = sameAirport;
            this.transferTime = transferTime;
            this.preFlightSimpleInfo = preFlightSimpleInfo;
            this.nextFlightSimpleInfo = nextFlightSimpleInfo;
        }
    }


    /**
     * 中转住宿服务标签
     *
     * @param segmentInfo1
     * @param segmentInfo2
     * @return
     */
    public boolean transitAccommodationApplicable(PtSegmentInfo segmentInfo1, PtSegmentInfo segmentInfo2) {

        // 申请规则
        List<RouteLabelDTO> routeLabels = getCacheRouteLabel();
        routeLabels = routeLabels.stream().filter(label -> "TransitAccommodation".equals(label.getLabelFunction())).collect(Collectors.toList());

        // 过滤航线标签启用日期、渠道
        routeLabels = filterRouteLabelsByDateAndChannel(routeLabels, ChannelCodeEnum.MOBILE.getChannelCode());

        return isCanApply(segmentInfo1, segmentInfo2, routeLabels);
    }


    /**
     *
     * @param segmentInfo1
     * @param segmentInfo2
     * @param finalMatchLabelRuleInfoList
     * @return
     */
    public boolean isCanApply(PtSegmentInfo segmentInfo1, PtSegmentInfo segmentInfo2, List<RouteLabelDTO> finalMatchLabelRuleInfoList) {
        // 将 PtSegmentInfo 转换为 FlightInfo 和 TransferDetails
        FlightInfo flightInfo = convertPtSegmentInfoToFlightInfo(segmentInfo1, segmentInfo2);
        TransferDetails transferDetails = convertPtSegmentInfoToTransferDetails(segmentInfo1, segmentInfo2);

        return finalMatchLabelRuleInfoList.stream().anyMatch(label ->
                label.getRuleDTOList().stream()
                        .filter(rule -> rule.isEnableStatus()) // 过滤启用的规则
                        .anyMatch(rule -> matchesRule(rule, flightInfo, transferDetails))
        );
    }




    /**
     * 将 PtSegmentInfo 转换为 FlightInfo
     */
    private FlightInfo convertPtSegmentInfoToFlightInfo(PtSegmentInfo segmentInfo1, PtSegmentInfo segmentInfo2) {
        FlightInfo flightInfo = new FlightInfo();

        // 设置基本航班信息（以第一段为主）
        flightInfo.setFlightDate(DateUtil.formatDate(DateUtil.parse(segmentInfo1.getDepTime())));
        flightInfo.setDepAirport(segmentInfo1.getDepAirportCode());
        flightInfo.setArrAirport(segmentInfo2.getArrAirportCode());
        // 无机型
        flightInfo.setFType(null);

        // 设置出发和到达国家信息
        AirPortInfoDto depAirportInfo = localCacheService.getLocalAirport(segmentInfo1.getDepAirportCode());
        AirPortInfoDto arrAirportInfo = localCacheService.getLocalAirport(segmentInfo2.getArrAirportCode());
        if (depAirportInfo != null) {
            flightInfo.setDeptCountryNo(depAirportInfo.getCountryNo());
        }
        if (arrAirportInfo != null) {
            flightInfo.setCountryNo(arrAirportInfo.getCountryNo());
        }

        // 设置航站楼信息
        flightInfo.setDepTerm(segmentInfo1.getDepAirportTerminal());
        flightInfo.setArrTerm(segmentInfo2.getArrAirportTerminal());

        // 设置承运航班号信息
        List<FlightNoIcon> carrierFlightNoIconList = new ArrayList<>();
        FlightNoIcon flightNoIcon1 = new FlightNoIcon();
        flightNoIcon1.setFlightNo(segmentInfo1.getFlightNo());
        carrierFlightNoIconList.add(flightNoIcon1);

        if (!segmentInfo1.getFlightNo().equals(segmentInfo2.getFlightNo())) {
            FlightNoIcon flightNoIcon2 = new FlightNoIcon();
            flightNoIcon2.setFlightNo(segmentInfo2.getFlightNo());
            carrierFlightNoIconList.add(flightNoIcon2);
        }
        flightInfo.setCarrierFlightNoIconList(carrierFlightNoIconList);

        return flightInfo;
    }

    /**
     * 将 PtSegmentInfo 转换为 TransferDetails
     */
    private TransferDetails convertPtSegmentInfoToTransferDetails(PtSegmentInfo segmentInfo1, PtSegmentInfo segmentInfo2) {
        // 计算中转信息
        Date seg1ArrTime = DateUtil.parse(segmentInfo1.getArrTime());
        Date seg2DepTime = DateUtil.parse(segmentInfo2.getDepTime());
        long transferTime = DateUtil.between(seg1ArrTime, seg2DepTime, DateUnit.MINUTE) * 60 * 1000; // 转换为毫秒

        String transfer = segmentInfo2.getDepAirportCode(); // 中转机场
        String dateLimit = DateUtil.formatDate(seg1ArrTime).equals(DateUtil.formatDate(seg2DepTime)) ? "sameDay" : "overnight";
        boolean sameAirport = segmentInfo1.getArrAirportCode().equals(segmentInfo2.getDepAirportCode());

        // 创建前后航班信息
        FlightSimpleInfo preFlightInfo = new FlightSimpleInfo();
        preFlightInfo.setFlightNo(segmentInfo1.getFlightNo());
        preFlightInfo.setDays(0); // 简化处理

        FlightSimpleInfo nextFlightInfo = new FlightSimpleInfo();
        nextFlightInfo.setFlightNo(segmentInfo2.getFlightNo());
        nextFlightInfo.setDays(DateUtil.formatDate(seg1ArrTime).equals(DateUtil.formatDate(seg2DepTime)) ? 0 : 1);

        return new TransferDetails(transfer, dateLimit, sameAirport, transferTime, preFlightInfo, nextFlightInfo);
    }

    /**
     * 验证适用航班号是否符合规则中的要求。
     *
     * @param rule 包含规则的对象，提供适用航班号的信息。
     * @param flightInfo 包含航班信息的对象，提供实际的航班号信息。
     * @param transferDetails 转机信息的对象，用于判断是否为中转航班。
     * @return 如果航班号符合规则中的要求，返回 true；否则返回 false。
     */
    private boolean isApplyFlightNoMatching(RouteLabelDTO.RouteLabelRuleDTO rule, FlightInfo flightInfo, TransferDetails transferDetails) {
        if (StringUtils.isBlank(rule.getApplyFlightNo())) {
            return true; // 为空时认为无限制，全航班号可用
        }

        List<String> applyFlightNos = Arrays.asList(rule.getApplyFlightNo().split(","));
        // 去除空格
        applyFlightNos = applyFlightNos.stream().map(String::trim).collect(Collectors.toList());
        boolean isTransferFlight = !DEFAULT.equals(transferDetails.transfer);

        if (isTransferFlight) {
            // 中转航班组合校验：校验两程的航班号是否均符合配置
            return applyFlightNos.stream().anyMatch(flightNoConfig -> {
                if (flightNoConfig.contains("-")) {
                    // 中转航班组合格式：HO1234-HO2222
                    String[] flightNoParts = flightNoConfig.split("-");
                    if (flightNoParts.length == 2 &&
                        transferDetails.preFlightSimpleInfo != null &&
                        transferDetails.nextFlightSimpleInfo != null) {

                        String preFlightNo = transferDetails.preFlightSimpleInfo.getFlightNo();
                        String nextFlightNo = transferDetails.nextFlightSimpleInfo.getFlightNo();

                        return flightNoParts[0].trim().equals(preFlightNo) &&
                               flightNoParts[1].trim().equals(nextFlightNo);
                    }
                }
                return false;
            });
        } else {
            // 直达航班：检查航班号是否在配置列表中
            if (CollectionUtils.isNotEmpty(flightInfo.getCarrierFlightNoIconList())) {
                List<String> finalApplyFlightNos = applyFlightNos;
                return flightInfo.getCarrierFlightNoIconList().stream()
                        .anyMatch(flightNoIcon -> finalApplyFlightNos.contains(flightNoIcon.getFlightNo()));
            }
        }

        return false;
    }

    /**
     * 验证适用机型是否符合规则中的要求。
     * 适用机型仅针对直达航线生效，如果是中转航班，则不生效。
     *
     * @param rule 包含规则的对象，提供适用机型的信息。
     * @param flightInfo 包含航班信息的对象，提供实际的机型信息。
     * @param transferDetails 转机信息的对象，用于判断是否为中转航班。
     * @return 如果机型符合规则中的要求，返回 true；否则返回 false。
     */
    private boolean isApplyAircraftTypeMatching(RouteLabelDTO.RouteLabelRuleDTO rule, FlightInfo flightInfo, TransferDetails transferDetails) {
        if (StringUtils.isBlank(rule.getApplyAircraftType())) {
            return true; // 不勾选任何机型时，认为无限制
        }

        boolean isTransferFlight = !DEFAULT.equals(transferDetails.transfer);
        if (isTransferFlight) {
            // 如果是中转航班，则适用机型内的配置不生效
            return true;
        }

        // 仅针对直达航线生效
        if (StringUtils.isNotBlank(flightInfo.getFType())) {
            List<String> applyAircraftTypes = Arrays.asList(rule.getApplyAircraftType().split(","));
            // 去除空格并转换为大写进行比较
            applyAircraftTypes = applyAircraftTypes.stream()
                    .map(type -> type.trim().toUpperCase())
                    .collect(Collectors.toList());
            return applyAircraftTypes.contains(flightInfo.getFType().toUpperCase());
        }

        return false;
    }

}


