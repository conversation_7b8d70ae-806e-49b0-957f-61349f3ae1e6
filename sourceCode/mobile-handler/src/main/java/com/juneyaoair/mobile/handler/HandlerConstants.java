package com.juneyaoair.mobile.handler;

import com.juneyaoair.utils.ConfigurableConstants;
import com.juneyaoair.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by qinxiaoming on 2016-4-13.
 */
public class HandlerConstants extends ConfigurableConstants {
    static {
        init("HandlerConfig.properties");
    }

    public static final String ENV = get("env")==null?"":get("env");
    public static final String SERVICE_CHARSET = "utf-8";
    public static final int SERVICE_TIME_OUT = 0;
    //官网
    public static final String URL_JUN_YAO_AIR = get("UnitOrder.JunYaoAir.URL");
    //官网机场查询接口
    public static final String AIRPORT_QUERY = "/QueryAirportAdminHttpHandler/QueryAirportAdmin";
    public static final String IS_CHECK_DEVICEID = StringUtil.isNullOrEmpty(get("is_checkDevice")) ? "N" : get("is_checkDevice");
    //运价系统
    public static final String URL_FARE = get("UnitOrder.WebAPI.URL");
    //订单系统oneOrder 网关
    public static final String New_URL_FARE = get("UnitOrder.NewAPI.URL");
    //订单系统用 open api
    public static final String URL_FARE_OPEN_API = get("UnitOrder.OpenAPI.URL");
    //crm java微服务 邮箱测试地址
    public static final String CRM_OPENAPI_URL = get("crm.openapi.url");
    public static final String CRM_COMPANY_OPENAPI_URL = get("crmcompany.openapi.url");
    public static final String CRM_MEMBER_OPENAPI_URL = get("crmmember.openapi.url");
    public static final String CRM_MILEAGE_OPENAPI_URL = get("crmmileage.openapi.url");
    public static final String CRM_API_VERSION = "10";
    public static final String MWEB_URL = get("mweb.url");
    public static final String MWEB_URL_NEW = get("mweb.url.new");//APP 5.0使用的是此地址
    //隐私条款地址
    public static final String POLICY_PROVISIONS_URL = get("policy.provisions.url");
    //"10"
    public static final String VERSION = StringUtils.isBlank(get("UnitOrder.WebAPI.VERSION")) ? "10" : get("UnitOrder.WebAPI.VERSION");
    // "CNY"
    public static final String CURRENCY_CODE = StringUtils.isBlank(get("UnitOrder.WebAPI.CURRENCY_CODE")) ? "CNY" : get("UnitOrder.WebAPI.CURRENCY_CODE");
    public static final String LANG_CODE = StringUtils.isBlank(get("UnitOrder.WebAPI.LANG_CODE")) ? "CN" : get("UnitOrder.WebAPI.LANG_CODE"); // "CN"
    //大数据信息配置
    public static final String BIGDATD_API_URL = get("bigdata_api_url");
    public static final String BIGDATA_CLIENTCODE = "CUXIAO";
    public static final String BIGDATA_CLIENT_PASSWORD = StringUtil.encodePwd("CUXIAO2018");
    public static final String BIGDATD_MEMBER_LABEL = "/traveller/customer/info";
    public static final String SEARCH_FLIGHT_DYNAMIC_INFO_URL = "/traveller/searchFlightDynamicsInfo";
    public static final String BIGDATD_MEMBER_TICKET = "/traveller/itinerary/info";
    // 旅客服务网
    public static final String URL_PASSAGER_API = get("UnitOrder.PassagerAPI.URL");
    //不正常航班记录查询
    public static final String FLIGHT_ABNORMALITY_QUERY = "/B2CManageHandler/FlightAbnormality";
    //不正常航班记录检查
    public static final String FLIGHT_ABNORMALITY_CHECK = "/B2CManageHandler/CheckFlightAbnormality";
    //运价接口
    public static final String QUERY_FLIGHT_FARE_V20 = "/Book/PublicQueryFlightFareV20"; // 航班运价查询方法
    public static final String QUERY_FARE_INFO_SIMPLE = "/Book/PublicQuerySimpleFare"; //2021-07-19 运价查询简化参数版本

    public static final String QUERY_SHOP_SEARCHONE = "/Shop/SearchOne"; // 按照运价来源查询运价
    public static final String QUERY_SHOP_SEARCHONEX = "/Shop/SearchOneX"; // 按照运价来源查询运价
    public static final String QUERY_MULTPLE_FLIGHT_FARE = "/Shop/SearchMultipleFlight"; // 多程运价查询方法
    public static final String QUERY_INTERNAT_TAX = "/Book/QueryInternatTax"; // 查询税费
    public static final String QUERY_FTRIDE_MPCON = "/Order/QueryFTRideMPCon"; //查询首乘会员价条件

    public static final String CHECK_UN_PAY_ORDER = "/Order/CheckUnPayOrder"; //订单数量限制

    public static final String TICKET_BOOK_V20 = "/Reserve/TicketBookV20";  //机票预订V20  java版
    public static final String RESERVE_BOOK_MULTIPLE = "/Reserve/TicketBookMultiple"; // 机票预订  java版 综合版
    //常用乘机人1.0
    public static final String QUERY_COMMON_PERSON = "/Book/QueryCommonPerson"; // 查询常用旅客信息
    public static final String MODIFY_COMMON_PERSON = "/Book/ModifyCommonPerson"; // 修改常用旅客信息
    public static final String ADD_COMMON_PERSON = "/Book/AddCommonPerson"; // 增加常用旅客信息
    //常用乘机人2.0
    public static final String QUERY_COMMON_PERSON_V20="/Book/QueryGeneralContact";//查询常用乘机人
    public static final String ADD_COMMON_PERSON_V20 = "/Book/AddGeneralContact"; // 增加常用旅客信息
    public static final String MODIFY_COMMON_PERSON_V20= "/Book/ModifyGeneralContact"; // 修改/删除常用旅客信息
    public static final String ADD_CERT_V20 = "/Book/AddGeneralCert"; // 增加证件信息
    public static final String MODIFY_CERT_V20 = "/Book/ModifyGeneralCert"; // 修改，删除证件信息

    public static final String CHANGE_FEE_CALCULATE = "/Order/CalculateChangeFee";//机票订单改期手续费计算


    public static final String ORDER_QUERY_PEERS = "/Order/QueryPeers"; // 单订儿童-通行人查询接口
    /**
     * 升舱费计算
     */
    public static final String CAL_TICKET_UPGRADE_FEE = "/Order/CalcTicketUpgradeFee";
    /**
     * 普通升舱
     * 国内
     */
    public static final String BOOK_TICKET_UPGRADE = "/Order/TicketUpgradeV20";
    /**
     * 普通升舱
     * 国际
     */
    public static final String BOOK_TICKET_INTER_UPGRADE = "/Order/TicketInterUpgradeV20";
    // 查询指定舱位信息，用于国际机票升舱
    public static final String BOOK_QUERY_UPGRADE_FLIGHT_INFO = "/Book/QueryUpgradeFlightInfo";
    //机票订单改期的接口V20
    public static final String TICKET_ORDER_CHANGE = "/Order/TicketOrderChangeV20";
    //多票号改期
    public static final String TICKET_CHANGE_MULTI = "/Order/TicketChangeMulti";
    //客票查询手续费用
    public static final String QUERY_CHANGE_FARE= "/Book/QueryFlightFareChange";

    //国际订单改期手续费查询
    public static final String INTER_CALCULATE_CHANGEFEE= "/Order/InterCalculateChangeFee";

    //国际客票改期手续费查询
    public static final String INTER_CALCULATE_TICKET_CHANGEFEE= "/Order/InterCalculateTicketChangeFee";

    public static final String SUB_QUERY_LOUNGE = "/Order/QueryLounge"; //查询贵宾信息室
    public static final String SUB_GET_LOUNGE = "/Order/GetLounge"; //订单查看贵宾信息室
    public static final String SUB_BUY_LOUNGE = "/Order/BuyLounge";/// 购买贵宾信息室
    public static final String SUB_REFUND_LOUNGE = "/Order/RefundLounge";/// 退单休息室
    public static final String SUB_QUERY_CHECKINTICKET = "/Order/QueryCheckInTicket"; //查询近期值机客票
    public static final String SUB_BUY_ONE_LOUNGE = "/Order/BuyOneLounge";/// 购买贵宾信息室

    public static final String SUB_QUERY_WIFI = "/Order/QueryWifi"; //查询wifi
    public static final String SUB_GET_WIFI = "/Order/GetWifi"; //查询wifi
    public static final String SUB_BUY_WIFI = "/Order/BuyWifi";/// 购买Wifi
    public static final String SUB_REFUND_WIFI = "/Order/RefundWifi";//退wifi

    public static final String SUB_QUERY_DELIVERY = "/Order/GetTicketDelivery";//查询
    public static final String SUB_BUY_DELIVERY = "/Order/BuyTicketDelivery";/// 购买行程单
    public static final String SUB_REFUND_TICKET_DELIVERY = "/Order/RefundTicketDelivery";//退行程单
    public static final String SUB_BUY_INSURE_APPLY = "/Order/InsuranceApply";/// 单独购保
    public static final String SUB_MEMBER_LEVEL_PROLONG_QUERY = "/Order/MemberLevelDelay/QueryProducts/v10";// 查询延期活动
    public static final String SUB_MEMBER_LEVEL_PROLONG_BOOK = "/Order/MemberLevelDelay/CreateCouponOrder/v10";// 查询延期活动

    public static final String SUB_QUERY_BAGGAGEEXCESS = "/Book/QueryBaggageExcess"; //查询逾重行李
    //优惠券活动
    public static final String SUB_QUERY_COUPON_ACTIVITY = "/Book/QueryCouponActivity"; // 优惠券活动查询
    public static final String SUB_QUERY_AVAIL_COUPON = "/Coupon/QueryAvailCoupon";/// 优惠券查询(可用)
    /** 优惠券查询(可用) JAVA版接口地址 */
    public static final String SUB_QUERY_AVAIL_COUPON_JAVA = "/Coupon/QueryAvailCoupon";
    public static final String SUB_QUERY_COUPON = "/Coupon/MyCouponQuery";/// 优惠券查询(ALL可用)
    public static final String SUB_CHECK_COUPON = "/Coupon/CheckCoupon";// 优惠券验证
    public static final String SUB_RECEIVE_COUPON = "/Coupon/ReceiveCoupon";// 优惠券领用
    public static final String SUB_SCORE_USE_RULE = "/Book/ScoreUseRuleQuery";// 积分使用规则查询
    public static final String QUERY_MULTIDISCOUNT = "/Book/QueryMultiDiscount";   //多乘客优惠查询
    public static final String QUERY_SCORECOUPON = "/Book/QueryScoreCoupon";   //积分兑换优惠劵活动查询
    public static final String BIND_COUPON = "/Book/BindCoupon";   //优惠劵不记名绑定
    public static final String CHANGE_COUPON = "/Book/ChangeCoupon";   //赠送优惠券
    public static final String CANCEL_CHANGE_COUPON = "/Book/CancelChangeCoupon";   //取消赠送优惠券
    public static final String RECEIVE_CHANGE_COUPON = "/Book/ReceiveChangeCoupon";   //领取他人赠送的优惠券
    public static final String AVAIL_UPGRADE_COUPON = "/Book/QueryAvailUpgradeCoupon";   //获取用户的升舱优惠券信息
    public static final String QUERY_COUPON_BY_CODE = "/Book/QueryCouponByCode";

    public static final String SUB_QUERY_ORDER_BRIEF = "/Order/QueryOrderTotalBrief";/// B2C查询综合订单概要

    public static final String ORDER_CHECK_WAIT_ORDER =   "/Order/CheckWaitOrder";

    public static final String SUB_QUERY_ORDER_CHANGE_BRIEF = "/Order/QueryOrderChangeBrief";/// 查询可改期订单
    public static final String SUB_QUERY_SUB_ORDER = "/Order/QuerySubOrder";/// 查询订单概要
    public static final String SUB_CANCEL_ORDER = "/Order/CancelOrder";//机票类订单取消
    public static final String SUB_CANCEL_TOUR_ORDER = "/Order/CancelTourOrder";//非机票类订单取消// 客票保险信息查询
    public static final String SUB_QUERY_ORDER_TRVEL = "/Order/QueryOrderNoTrvel"; //待出行客票
    public static final String SUB_QUERY_ORDER_COMMENT = "/Order/QueryQueryOrderNoComments"; //待评价客票
    public static final String SUB_GETBAGGAGE = "/Order/GetBaggage"; //逾重行李查询
    public static final String COUNT_UNPAY_ORDER= "/Order/CountUnpayOrder"; //统计未支付订单数
    /**
     *票号提取接口
     **/
    public static final String QUERY_TICKET_INFO = "/Order/GetTicketInfo";
    public static final String GET_TICKETINFO_INSURANCE = "/Order/GetTicketInfoWithInsurance";
    //根据证件查询客票状态
    public static final String QUERY_TICKET_DIGEST_INFO = "/Order/GetTicketDigestInfo";

    public static final String QUERY_INSURANCE_STATE = "/Order/QueryInsuranceState";
    public static final String EXCESSBAGGAGE_INVOICE_URL = "/ho-cc/ccTicketDelivery/saveFeign";//逾重行李开发票接口
    //我的吉豆
    public static final String SUB_QUERY_BEAN = "/Order/QueryBean"; // 吉豆查询
    // 退票管理API
    public static final String SUB_QUERY_REFUND_APPLY = "/Refund/RefundApply";/// 查询退票申请

    public static final String SEARCH_THEME_FLIGHT_INFO_LIST = "/basic/searchThemeFlightInfoList";// 查询原神主题航班

    public static final String SEARCH_THEME_FLIGHT_BY_DATE = "/basic/searchThemeFlightByDate";// 根据航班日期查询主题航班计划

    public static final String SELECT_THEME_CALENDAR = "/basic/selectThemeCalendar";// 主题航班日历

    public static final String CALCULATE_REFUND = "/Refund/CalculateRefund"; // 退票费计算
    public static final String BATCH_REFUND_CONFIRM = "/Refund/BatchRefundConfirm"; // 批量退票确认
    public static final String SUB_REFUND_INSURE = "/Refund/RefundInsurance"; /// 单独退保
    public static final String SUB_QUERY_TOTAL_REFUND = "/Refund/QueryTotalRefundInfo";/// B2C查询所有退单
    public static final String SUB_QUERY_REFUND_DETAIL = "/Refund/QueryRefundTicketDelNew";/// B2C查询退单详细(新)
    public static final String SUB_QUERY_INSURE_DETAIL = "/Refund/QuerySurrenderDetail";//查询单独退保订单
    public static final String SUB_QUERY_REFUND_COUNT = "/Refund/QueryCustomerTicketPayoutCount"; //查询客户退票次数
    public static final String SUB_QUERY_REFUND_DETAILS = "/Refund/QueryRefundDetail";
    public static final String SUB_QUERY_CHANGED_REFUND = "/Order/QueryChangedOrderInfo";//根据原订单查询改期订单是否已经退
    //特殊服务
    public static final String SPECIAL_SERVICE_APPLY = "/Service/SpecialServiceApply";//特殊
    public static final String SPE_SERVICE_APPLY = "/passenger/speServiceApplication/spePassengerService";//特服
    public static final String SPECIAL_SERVICE_QUERY = "/Service/SpecialServiceQuery";//我的申请和订单详情查询
    public static final String SPECIAL_SERVICE_CANCEL = "/Service/SpecialServiceCancel";//我的申请和订单详情查询
    // 电子补偿
    public static final String PASSAGER_QUERY = "/passenger/elcpElectronicCompensation/selectElcpElectronicCompensationBtc"; // 不正常航班查询
    public static final String PASSAGER_COMPARE = "/passenger/elcpElectronicCompensation/comparePassenger"; // 电子补偿旅客比对信息查询

    public static final String QUERY_COMPLETE_FLIGHT_INFO = "/passenger/elcpElectCompScheme/selectElcpElectCompSchemeBtc"; // 整机补偿时查询航班信息

    public static final String ADD_COMPLETE_FLIGHT_PASSENGER = "/passenger/elcpElectronicCompensation/addSingleElcpElectronicCompensation"; // 整机补偿时 新增旅客信息
    public static final String PASSAGER_CONFIRM = "/passenger/elcpElectronicCompensation/confirmInfo"; // 旅客比对信息确认接口
    public static final String SELECT_COMPENSTATION_BY_ID = "/passenger/celcpElecCompensation/selectCompensationById"; // 根据id查询通用补偿（新版不正常航班查询）
    public static final String COMPENSATION_APPLY = "/passenger/elcpElectronicCompensation/approvalRequiredCompensation"; // 收款申请

    public static final String COMPENSATE_APPLY = "/passenger/payOutNotice/payOutCheckNotice"; // 收款申请

    public static final String WXPAYOUT_CHECKNOTICE = "/passenger/payOutNotice/wxPayOutCheckNotice"; // 微信支付接口
    public static final String ADD_COMPENSATION = "/passenger/celcpElecCompensation/addCelcpElecCompensationB2C"; // 旅客提交补偿申请-B2C

    public static final String TO_CHECK_REAL_NAME_INFO = "/passenger/payOutNotice/validateAuthCode"; // 旅服实名认证验证
    public static final String SELECT_ABLG_BY_ID = "/passenger/ablgAbnormalLuggage/selectAblgById"; // 不正常行李查询接口
    public static final String COMMIT_ABLG = "/passenger/ablgAbnormalLuggage/commitAblgB2C"; // 提交不正常行李赔付信息
    public static final String SELECT_SPERVAPP_LOCK = "/passenger/speServApplLock/selectSpervAppLock";//查询剩余无陪儿童剩余配额

    //地面候补
    /**
     * 可地面候补的航班
     */
    public static final String WAITINGPASSAGER_QUERYFLIGHT = "/passenger/waitWaitingPassenger/selectWaitWaitingFlightCan";
    /**
     * 地面候补申请
     */
    public static final String WAITINGPASSAGER_APPLY = "/passenger/waitWaitingPassenger/addWaitWaitingPassengerList";
    /**
     * 地面候补人数查询
     */
    public static final String WAITINGPASSAGER_SELECTNUM = "/passenger/waitWaitingPassenger/selectWaitWaitingPassengerNum";
    //行李标准查询
    public static final String BAGGAGE_STANDARD_QUERY = "/passenger/luggParamSet/selectLuggStandard";
    //超规行李接口
    public static final String OVER_SPECIFICATION_BAGGAGE_APPLY = "/passenger/oslgOutOfSpecLuggage/addOutOfSpecLuggage";
    //查询是否支持航班书籍阅读
    public static final String QUERY_SUPPORT_FLIGHT_BOOK = "/passenger/pscbCsmBookFlt/selectfltSup";
    //航班图书信息查询
    public static final String QUERY_FLIGHT_BOOKS = "/passenger/pscbCsmBookInf/selectBookPage";
    //新增旅客借阅信息
    public static final String APPLY_BOOK_ADD = "/passenger/pscbCsmBookPsr/addPscbCsmBookPsr";
    // 遗失物品登记申请记录查询
    public static final String LOST_ITEMS_APPLY_RECORD_QUERY = "/passenger/lvbhReportLoss/getReportLossStatus";
    // 取消申请接口
    public static final String LOST_ITEMS_CANCEL_APPLY = "/passenger/lvbhReportLoss/cancelReportLoss";

    //权益券
    /**
     * 可售优惠券查询
     */
    public static final String SALE_COUPON_QUERY = "/Book/QuerySaleCoupon";
    /**
     * 新可售优惠劵查询
     */
    public static final String NEW_ALE_COUPON_QUERY = "/Order/QuerySaleCoupon";

    /**
     * 可售优惠券购买
     */
    public static final String SALE_COUPON_BUY = "/Book/BuySaleCoupon";
    /**
     * 新可售优惠劵购买
     */
    public static final String NEW_SALE_COUPON_BUY = "/Order/BuySaleCoupon";
    /**
     * 我的可售券订单查看  传入订单编号时用于查询详情
     */
    public static final String SALE_COUPON_LISTORDER = "/Book/GetMySaleCouponOrder";
    /**
     * 新我的可售劵订单查看 传入订单编号时用于查询详情
     */
    public static final String NEW_SALE_COUPON_LISTORDER = "/Order/GetMySaleCouponOrder";
    /**
     * 新可售优惠劵申退
     */
    public static final String NEW_SALE_COUPON_REFUND = "/Order/RefundSaleCoupon";
    /**
     * 权益订单取消
     */
    public static final String SALE_COUPON_CANCEL = "/Order/CancelTourOrder";
    /**
     * 我的权益券
     */
    public static final String SALE_COUPON_LIST = "/Book/GetMySaleCoupon";
    /**
     * 订单删除
     */
    public static final String SALE_COUPON_REMOVE = "/Order/RemoveOrder";

    /**
     * 检查客票信息
     */
    public static final String TICKET_INFO_FLIGHT_CHECK = "/Order/TicketFlightCheck";
    /**
     * B2C查询综合订单简要
     */
    public static final String ORDER_BRIEF = "/Order/QueryOrderTotalBrief";
    /**
     * B2C查询综合订单简要 2021-03-03
     */
    public static final String ORDER_ERROR_BUY_BRIEF = "/Order/QueryErrorBuyOrderTotalBrief";
    /**
     * 删除取消订单
     */
    public static final String DELETE_CANCEL_ORDER = "/Order/DeleteCancelOrder";
    /**
     * 待出行订单查询
     */
    public static final String NEW_SUB_QUERY_ORDER_TRVEL = "/Order/QueryOrderNoTrvel";
    /**
     * 待评价订单查询
     */
    public static final String NEW_SUB_QUERY_ORDER_COMMENT = "/Order/QueryQueryOrderNoComments";
    /**
     * 我的可售券订单查看
     */
    public static final String COUPON_ORDER_PRODUCT= "/Order/GetMyCouponProductOrder";

    /**
     * 主题卡兑换机票是否出行
     */
    public static final String QUERY_USED_SEGBY_CODE= "/Order/UnlimitedFly/QueryUsedSegByCode";

    /** 新订单详情查询接口 */
    public static final String BASIC_GET_COUPON_ORDER = "/Order/BasicGetCouponOrder";
    /** 新查询订单退单详情 */
    public static final String BASIC_GET_COUPON_REFUND = "/Order/BasicGetCouponRefund";
    public static final String QUERY_COUPON_ORDER_LIST= "/Order/QueryCouponOrderList";
    /**
     * 取消未支付订单
     */
    public static final String CANCEL_COUPON_ORDER_PRODUCT = "/Order/CancelOrder";
    /**
     * 权益券产品申请退款
     */
    public static final String REFUND_PRIDUCT_APPLY = "/Order/RefundCouponProduct";


    public static final String BASIC_REFUND_COUPON_ORDER = "/Order/BasicRefundCouponOrder";

    /**
     * 查询航班日期最大的订单客票信息
     */
    public static final String SUB_QUERY_ORDER_MAX = "/Order/QueryMaxFlightDateOrder";
    /**
     * 权益券预约
     */
    public static final String RESERVE_COUPON = "/Order/ReserveVoucherCouponProduct";
    public static final String VOUCHER_COUPON_GIVE = "/Order/GiveVoucherCouponProduct";//发起转赠(产品管理系统)
    public static final String NEW_VOUCHER_COUPON_GIVE = "/GiveThemeCardController/GiveCard";//主题卡发起转赠(产品管理系统)
    public static final String VOUCHER_COUPON_ACCEPT= "/Order/AcceptVoucherCouponProduct";//接收转赠(产品管理系统)
    public static final String VOUCHER_COUPON_CANCEL= "/Order/CancelGiveVoucherCouponProduct";//取消转赠(产品管理系统)
    /** 券码兑换 */
    public static final String REDEM_EQUITY_UNITS= "/approvalOrder/redemEquityUnits";
    //网关查询url
    public static final String URL_GATEWAY_QUERY = get("queryGatway.url");
    public static final String URL_PAY = get("payment.url");
    public static final String URL_PAYMETHODS = get("payMethods.url");
    public static final String URL_ASYNC_PAY = get("payment.async.url");
    public static final String LIMIT_PAY_BY_SHOUFUYOU = get("payment.shoufuyou.limit.url");
    //华瑞
    public static final String URL_INIT_BY_HUARUI = get("payment.hrbank.init.url");

    //支付后台返回地址
    public static final String BACK_RETURN_URL_PAY = get("payment.back.url");
    public static final String MWEB_SHOUFUYOU_RETURN_URL_PAY = get("mweb.payment.shoufuyou.back.url");
    public static final String VIRTUAL_GATEWAYNO = get("virtual.pay.gatewayNo");
    public static final String VIRTUAL_CARDINFO = get("virtual.pay.cardInfo");
    public static final String MWEB_PAY_RETURN_URL = get("mweb.pay.return.url");//网页支付回调地址

    //客票直退
    public static final String SPEEDTICKET_QUERY_URL = "/Order/QueryRefundTicket";//我的极速退票
    public static final String SPEEDTICKET_REQ_URL = "/Order/RefundTicket";//退票申请
    public static final String SPEEDTICKET_REFUND_RETRL = "/Order/RefundTicketRetry";//重试提交
    public static final String SPEEDTICKET_REFUND_DETAIL = "/Order/QueryRefundTicketDetail";//快速退票查看指定订单详情
    //网上值机状态
    public static final String CHECKIN_STATUS_YES = "AC";//已值机
    public static final String CHECKIN_STATUS_IN = "AD";//可值机
    public static final String CHECKIN_STATUS_NO = "AE";//待值机
    public static final String CHECKIN_STATUS_INVALID = "NA";//不可值机的状态

    //运价查询
    public static final String ROUTE_TYPE_OW = "OW";    //单程
    public static final String ROUTE_TYPE_RT = "RT";        //往返
    public static final String ROUTE_TYPE_CT = "CT";        //中转

    //飞行方向
    public static final String FLIGHT_DIRECTION_GO = "G";//去程
    public static final String FLIGHT_DIRECTION_BACK = "B";//返程

    public static final String PRICE_PRODUCT_TYPE_PUBLIC = "1";//运价系统 运价产品 公布
    public static final String PRICE_PRODUCT_TYPE_PRIVATE = "2";//运价系统 运价产品 私有
    public static final String PRICE_PRODUCT_TYPE_MANY_SEGMENT = "3";//运价系统 运价产品 多程惠达
    public static final String PRICE_PRODUCT_TYPE_TRANS_SEGMENT = "4";    //运价系统 运价产品 中转联程
    public static final String FLIGHT_DIRECT_D = "D";    //中转方案	D － 只查询直达航班和运价
    public static final String FLIGHT_DIRECT_T = "T";    //中转方案	T － 没有直达方案时，给出中转方案运价
    public static final String FLIGHT_DIRECT_S = "S";    //中转方案	S － 没有直达方案时,只给出中转航程，不查询航班可利用座位和运价
    public static final String TRIP_TYPE_D = "D";    //国内
    public static final String TRIP_TYPE_I = "I";    //国际
    //港澳台
    public static final String TRIP_TYPE_R = "R";

    public static final String TRIP_ALL = "ALL";
    public static final String TRIP_DOMESTIC = "DOMESTIC";    //国内
    public static final String TRIP_INTL = "INTL";    //国际

    public static final String PASSENGER_TYPE_CHD = "CHD";
    public static final String PASSENGER_TYPE_ADT = "ADT";
    public static final String PASSENGER_TYPE_INF = "INF";
    public static final String PASSENGER_TYPE_OLD = "OLD";
    public static final String PASSENGER_TYPE_GMJC = "GMJC";//军残警残

    public static final String FLIGHT_INTER_I = "I";
    public static final String FLIGHT_INTER_D = "D";

    public static final String INSURE_STATE_APPLY = "Apply"; /// 待购保
    public static final String INSURE_STATE_SUCCESS = "Success";  /// 已购保

    //客票状态
    public static final String OPEN_FOR_USE = "OPEN FOR USE";   /// 客票有效，可以使用
    public static final String USED_FLOWN = "USED/FLOWN"; /// 客票已使用
    public static final String EXCHANGED = "EXCHANGED"; /// 客票已换开到其它电子票上
    public static final String SUSPENDED = "SUSPENDED";/// 挂起
    public static final String REFUNDED = "REFUNDED";/// 退票
    public static final String VOID = "VOID";         /// 作废
    public static final String FIM_EXCH = "FIM EXCH";/// 客票已换开为飞行中断旅客舱单
    public static final String PRINT_EXCH = "PRINT EXCH";/// 客票已换开为纸票
    public static final String BOARDED = "BOARDED";   //客票已登机
    public static final String CHECKED_IN = "CHECKED IN";/// 客票已值机
    public static final String AIRP_CNTL = "AIRP CNTL";/// 客票权限归属其他航司
    public static final String THEM = "THEM";//多次卡
    //访问控制
    public static final String AV_SOURCE = get("Juneyao.Visit.Type.Av");
    public static final String AV_SOURCE_CNT = get("Juneyao.Visit.Cnt.Av");
    public static final String AV_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.Av");

    public static final String REG_SOURCE = get("Juneyao.Visit.Type.Reg");
    public static final String REG_SOURCE_CNT = get("Juneyao.Visit.Cnt.Reg");
    public static final String REG_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.Reg");

    //短信通用限制
    public static final String SMS_COMMON_SOURCE = get("Juneyao.Visit.Type.Sms.Common");
    public static final String SMS_COMMON_SOURCE_CNT = get("Juneyao.Visit.Cnt.Sms.Common");
    public static final String SMS_COMMON_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.Sms.Common");

    public static final String SMS_SOURCE = get("Juneyao.Visit.Type.Sms");
    public static final String SMS_SOURCE_CNT = get("Juneyao.Visit.Cnt.Sms");
    public static final String SMS_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.Sms");

    public static final String EMAIL_SOURCE = get("Juneyao.Visit.Type.Email");
    public static final String EMAIL_SOURCE_CNT = get("Juneyao.Visit.Cnt.Email");
    public static final String EMAIL_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.Email");



    public static final String COMM_SOURCE = get("Juneyao.Visit.Type.comm");
    public static final String COMM_SOURCE_CNT = get("Juneyao.Visit.Cnt.comm");
    public static final String COMM_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.comm");
    public static final String LOGINERR_SOURCE = get("Juneyao.Visit.Type.LoginErr");
    public static final String LOGINERR_LIMIT_SOURCE = get("Juneyao.Visit.Cnt.LoginErr");
    public static final String LOGINCODE_IP_SOURCE = get("Juneyao.Visit.Type.LoginCodeIP");//IP获取验证码
    public static final String LOGINCODE_IP_LIMIT_SOURCE = get("Juneyao.Visit.Cnt.LoginCode_IP");//IP获取验证码次数
    public static final String LOGINCODE_MOBILE_SOURCE = get("Juneyao.Visit.Type.LoginCodeMobile");//账号获取验证码
    public static final String LOGINCODE_MOBILE_LIMIT_SOURCE = get("Juneyao.Visit.Cnt.LoginCode_MOBILE");//账号获取验证码次数

    public static final String NAMEAUTH_MOBILE_SOURCE = get("Juneyao.Visit.Type.NameAuth");//手机获取验证码
    public static final String NAMEAUTH_MOBILE_SOURCE_CNT = get("Juneyao.Visit.Cnt.NameAuth");//手机获取验证码
    public static final String NAMEAUTH_MOBILE_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.NameAuth");//手机获取验证码
    public static final String NAMEAUTH_IP_SOURCE = get("Juneyao.Visit.Type.NameAuthIP");//IP获取验证码
    public static final String NAMEAUTH_IP_SOURCE_CNT = get("Juneyao.Visit.Cnt.NameAuthIP");//IP获取验证码次数
    public static final String NAMEAUTH_IP_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.NameAuthIP");//IP获取验证码永久次数
    public static final String NAMEAUTH_MOBILEERR_SOURCE = get("Juneyao.Visit.Type.NameAuthErr");//手机获取验证码错误限制
    public static final String NAMEAUTH_MOBILEERR_SOURCE_CNT = get("Juneyao.Visit.Cnt.NameAuthErr");//手机获取验证码错误次数

    public static final String LOGIN_SOURCE = get("Juneyao.Visit.Type.Login");
    public static final String LOGIN_SOURCE_CNT = get("Juneyao.Visit.Cnt.Login");

    /**
     * 实名认证上传照片限制
     */
    //上传照片账号限制
    public static final String PHOTOAUTH_MOBILE_SOURCE = get("Juneyao.Visit.Type.PhotoAuth");
    public static final String PHOTOAUTH_MOBILE_SOURCE_CNT = get("Juneyao.Visit.Cnt.PhotoAuth");
    public static final String PHOTOAUTH_MOBILE_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.PhotoAuth");
    //上传照片IP限制
    public static final String PHOTOAUTH_IP_SOURCE = get("Juneyao.Visit.Type.PhotoAuthIP");
    public static final String PHOTOAUTH_IP_SOURCE_CNT = get("Juneyao.Visit.Cnt.PhotoAuthIP");
    public static final String PHOTOAUTH_IP_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.PhotoAuthIP");
    //上传照片设备限制
    public static final String PHOTOAUTH_DEVICE_SOURCE = get("Juneyao.Visit.Type.PhotoAuthDevice");
    public static final String PHOTOAUTH_DEVICE_SOURCE_CNT = get("Juneyao.Visit.Cnt.PhotoAuthDevice");
    public static final String PHOTOAUTH_DEVICE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.PhotoAuthDevice");
    // 修改密码
    public static final String UPDATEPWD_SOURCE = get("Juneyao.Visit.Type.UpdatePwd");
    public static final String UPDATEPWD_SOURCE_CNT = get("Juneyao.Visit.Cnt.UpdatePwd");
    public static final String UPDATEPWD_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.UpdatePwd");
    // 忘记密码
    public static final String FORGETPWD_SOURCE = get("Juneyao.Visit.Type.ForgetPwd");
    public static final String FORGETPWD_SOURCE_CNT = get("Juneyao.Visit.Cnt.ForgetPwd");
    public static final String FORGETPWD_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.ForgetPwd");
    // 修改消费密码
    public static final String CONSUME_SOURCE = get("Juneyao.Visit.Type.Consume");
    public static final String CONSUME_SOURCE_CNT = get("Juneyao.Visit.Cnt.Consume");
    public static final String CONSUME_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.Consume");
    // 修改手机号码
    public static final String MOBILE_SOURCE = get("Juneyao.Visit.Type.Mobile");
    // 新增手机号码
    public static final String ADD_MOBILE_SOURCE = get("Juneyao.Visit.Type.AddMobile");
    public static final String MOBILE_SOURCE_CNT = get("Juneyao.Visit.Cnt.Mobile");

    public static final String ADD_MOBILE_SOURCE_CNT = get("Juneyao.Visit.Cnt.AddMobile");
    public static final String MOBILE_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.Mobile");

    public static final String ADD_MOBILE_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.AddMobile");
    // 值机次数限制
    public static final String CHECKIN_SOURCE = get("Juneyao.Visit.Type.CheckIn");
    public static final String CHECKIN_SOURCE_CNT = get("Juneyao.Visit.Cnt.CheckIn");
    public static final String CHECKIN_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.CheckIn");
    // 总次数限制
    public static final String TOTAL_SOURCE = get("Juneyao.Visit.Type.Total");
    public static final String TOTAL_SOURCE_CNT = get("Juneyao.Visit.Cnt.Total");
    //活动次数限制
    public static final String ACTIVITY_SOURCE = get("Juneyao.Visit.Type.Activity");
    public static final String ACTIVITY_SOURCE_CNT = get("Juneyao.Visit.Cnt.Activity");
    public static final String ACTIVITY_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.Activity");
    //领取赠送优惠券限制
    public static final String GIVECOUPON_SOURCE = get("Juneyao.Visit.Type.GiveCoupon");
    public static final String GIVECOUPON_SOURCE_CNT = get("Juneyao.Visit.Cnt.GiveCoupon");
    public static final String GIVECOUPON_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.GiveCoupon");
    //手机配置
    public static final String M_USER_NO = get("UnitOrder.WebAPI.M.USER_NO");
    public static final String M_CHANNEL_CODE = get("UnitOrder.WebAPI.M.CHANNEL_CODE");
    public static final String M_CHANNEL_CODE_INT = get("UnitOrder.WebAPI.M.CHANNEL_CODE_INT");
    public static final String M_USER_KEY = get("UnitOrder.WebAPI.M.USER_KEY");
    public static final String M_CPS_KEY = get("UnitOrder.WebAPI.M.CPS_KEY");
    public static final String M_CLIENT_PWD = get("mobile.client.passwd");
    public static final String M_CHECKIN_CODE = get("mobile.checkin.code");

    //微信配置
    public static final String WEIXIN_APP_ID = get("weixin.app.id");
    public static final String WEIXIN_SECRET = get("weixin.secret");
    public static final String WEIXIN_LOGIN = get("weixin.login");

    public static final String WEIXIN_GRANT_TYPE = "authorization_code";

    public static final String W_USER_NO = get("UnitOrder.WebAPI.W.USER_NO");
    public static final String W_CHANNEL_CODE = get("UnitOrder.WebAPI.W.CHANNEL_CODE");
    public static final String W_CHANNEL_CODE_INT = get("UnitOrder.WebAPI.W.CHANNEL_CODE_INT");
    public static final String W_USER_KEY = get("UnitOrder.WebAPI.W.USER_KEY");
    public static final String W_CPS_KEY = get("UnitOrder.WebAPI.W.CPS_KEY");
    public static final String W_CLIENT_PWD = get("weixin.client.passwd");
    public static final String W_CHECKIN_CODE = get("weixin.checkin.code");
    public static final String WEIXIN_MEMBER_SAVE = get("weixin.member.save");
    public static final String WEIXIN_MEMBER_SAVE_URL = "/externalService/saveWXPlatformUserInfos";
    public static final String WEIXIN_SAVE_TEMPLATE_AUTHORIZE = "/externalService/saveTemplateAuthorize";
    public static final String WEIXIN_QUERY_BIND_RECORD_URL = "/externalService/queryWxUserRecord";
    public static final String WEIXIN_MEMBER_UNBIND_URL = "/externalService/unbindWxUser";
    public static final String CREATE_SHORT_URL = "/externalService/createWXShortUrl";
    public static final String GET_USERINFO_FOR_OPENID_NOTFOUND = "/externalService/getUserInfoForOpenidNotFound";

    public static final String WXAPP_CLIENT_PWD = get("wxapp.client.passwd");
    public static final String MWEB_CLIENT_PWD = get("mweb.client.passwd");
    //华为5G
    public static final String HW5G_CLIENT_PWD = get("hw5g.client.passwd");
    public static final String MP_ALIPAY_CLIENT_PWD = get("mpalipay.client.passwd");
    public static final String HW5G_AES_SECRET = get("hw5g.aes.secret");
    //PDA手持设备自助值机
    public static final String PDA_CHECKIN_CODE = get("pda.checkin.code");
    public static final String PDA_CHANNEL_CODE = "PDA";

    //B2C配置
    public static final String B2C_USER_NO = get("UnitOrder.WebAPI.B2C.USER_NO");
    public static final String B2C_CHANNEL_CODE = get("UnitOrder.WebAPI.B2C.CHANNEL_CODE");

    //CRM活动配置
    public static final String CRM_USER_NO = get("UnitOrder.WebAPI.CRM.USER_NO");
    public static final String CRM_CHANNEL_CODE = get("UnitOrder.WebAPI.CRM.CHANNEL_CODE");

    //接口验证key
    public static final String USER_INFO_KEY = get("user.info.key");
    public static final String USER_INFO_KEY_M = get("user.info.key.m");

    public static final String INSURE_INFO_D = getProperty("insure.info.D", "");
    public static final String INSURE_INFO_I_OW = getProperty("insure.info.I.OW", "");
    public static final String INSURE_INFO_I_RT = getProperty("insure.info.I.RT", "");

    //多图片上传地址
    public static final String MULTI_IMAGE_PATH = get("multi.uploadImage.path");

    //老的管理接口
    public static final String CONSOLE_URL = getProperty("console.url", "");
    public static final String CONSOLE_PUSH_PATH = "/managePush/addPush";
    public static final String CONSOLE_PUSH_PATH_BATCH = "/managePush/addPushBatch";//APP批量推送
    public static final String MESSAGE_URL = "/restMessage/getMessageList";
    public static final String MESSAGEUSER_URL = "/restMessage/delMessageUser";
    public static final String SPECIALCITY_URL = "/restMessage/getSpecialCityList";
    public static final String REFRESH_SPECIALCITY_URL = "/restMessage/refreshCityList";//旅随心动 换一批
    //评价类型
    public static final String EVALBYORDER = "OE";
    public static final String EVALBYAIRLINE = "AE";

    //遗失物品请求传输至旅客服务网
    public static final String GOODS_LOST_URL = get("goods.lost.url");
    public static final String GOODS_LOST_SOURCE = get("Juneyao.Visit.Type.Goods_Lost");
    public static final String GOODS_LOST_SOURCE_CNT = get("Juneyao.Visit.Cnt.Goods_Lost");
    //无陪儿童请求传输至B2C
    public static final String UNACCOMPANIED_MINORWEB_URL = get("unaccompanied.minorWeb.url");
    public static final String UNACCOMPANIED_MINOR_APPLY="/Order/ChildrenOrderApply";//无陪儿童服务申请地址
    //新的短信平台
    public static final String SMS_USER = get("Juneyao.Sms.userId");
    public static final String SMS_PWD = get("Juneyao.Sms.NPwd");
    public static final String SMS_TYPE = get("Juneyao.Sms.Type");
    //双十一
    public static final String REQURL_INTERFACE = get("interfaceReqUrlPre");
    public static final String PRICECACHE_REQURL = get("priceCacheReqUrl");
    public static final String INTERFACE_LUCKYDRAW = get("interfaceLuckyDraw");

    //按月获取价格日历
    public static final String GETEE_MONTHLY_URL = get("priceCacheMonthlyReqUrl");
    //会员卡配置
    //默认配置
    public static final String DEFAULT = "/templet/common.png";
    public static final String NEW_COMMON_ECARD = StringUtil.isNullOrEmpty(get("newcommon.templet.url")) ? DEFAULT : get("newcommon.templet.url");
    public static final String NEW_SILVER_ECARD = StringUtil.isNullOrEmpty(get("newsilver.templet.url")) ? DEFAULT : get("newsilver.templet.url");
    public static final String NEW_GOLDEN_ECARD = StringUtil.isNullOrEmpty(get("newgolden.templet.url")) ? DEFAULT : get("newgolden.templet.url");
    public static final String NEW_BLACK_ECARD = StringUtil.isNullOrEmpty(get("newblack.templet.url")) ? DEFAULT : get("newblack.templet.url");
    public static final String DOCTOR_SILVER_ECARD = StringUtil.isNullOrEmpty(get("doctorsilver.template.url")) ? DEFAULT : get("doctorsilver.template.url");
    public static final String DOCTOR_GOLDEN_ECARD = StringUtil.isNullOrEmpty(get("doctorgolden.template.url")) ? DEFAULT : get("doctorgolden.template.url");
    public static final String OUTPUT_FILE = get("out.ecard.url");

    //获取ftptomcate的端口号
    public static final String FTP_TOMCAT_PORT_01 = get("ftp_tomcate_port_01");
    //获取ftp服务器1的ip
    public static final String FTP_HOSTNAME_01 = get("ftp_address_01");
    //获取ftp服务器1的端口
    public static final int FTP_PORT_01 = Integer.parseInt(get("ftp_port_01"));
    //获取ftp服务器1的用户名
    public static final String FTP_USERNAME_01 = get("ftp_username_01");
    //获取ftp服务器1的密码
    public static final String FTP_PASSWORD_01 = get("ftp_password_01");
    //ftp服务器1文件的目录
    public static final String FTP_PATHNAME_01 = get("ftp_pathname_01");
    public static final String FTP_IMG_PATH_01 = get("ftp_img_path_01");
    //获取sftptomcate的端口号
    public static final String SFTP_TOMCAT_PORT_01 = get("sftp_tomcate_port_01");
    //获取sftp的ip
    public static final String SFTP_HOSTNAME_01 = get("sftp_address_01");
    //获取sftp的端口
    public static final int SFTP_PORT_01 = Integer.parseInt(get("sftp_port_01"));
    //获取sftp的用户名
    public static final String SFTP_USERNAME_01 = get("sftp_username_01");
    //获取sftp的密码
    public static final String SFTP_PASSWORD_01 = get("sftp_password_01");
    //sftp上存放文件的目录
    public static final String SFTP_PATHNAME_01 = get("sftp_pathname_01");
    public static final String SFTP_IMG_PATH_01 = get("sftp_img_path_01");
    //淘旅行订单地址
    public static final String TAOLX_URL = get("taolx_url");
    public static final String TAOLX_URL_METHOD = "Order.OrderService.GetPackageList";//获取度假订单列表
    //内部IP正则表达式
    public static final String IP_PATTERN = get("ip.company.pattern");
    //支付宝实名认证
    public static final String ALIPAY_APP_ID = get("alipay_app_id");
    //领优惠券IP控制
    public static final String RECEIVE_COUPONS_PASS_IP = get("receive.coupons.pass.ip");
    //接口访问token密钥
    public static final String ACCESSSECRET = StringUtil.isNullOrEmpty(get("access.secret")) ? "juneyaoair" : get("access.secret");
    //客户端版本访问控制
    public static final String SERVER_SURPORT_CLIENT = StringUtil.isNullOrEmpty(get("server.surport.client")) ? "" : get("server.surport.client");
    //微信绑定,会员登录
    public static final String BIND_GEETEST_ID = StringUtil.isNullOrEmpty(get("bind.geetest.id")) ? "" : get("bind.geetest.id");
    public static final String BIND_GEETEST_KEY = StringUtil.isNullOrEmpty(get("bind.geetest.key")) ? "" : get("bind.geetest.key");
    //通用初始化
    public static final String INITIALIZE_GEETEST_ID = StringUtil.isNullOrEmpty(get("initialize.geetest.id")) ? "" : get("initialize.geetest.id");
    public static final String INITIALIZE_GEETEST_KEY = StringUtil.isNullOrEmpty(get("initialize.geetest.key")) ? "" : get("initialize.geetest.key");
    //APP生成订单
    public static final String ORDERBOOK_GEETEST_ID = StringUtil.isNullOrEmpty(get("orderBook.geetest.id")) ? "" : get("orderBook.geetest.id");
    public static final String ORDERBOOK_GEETEST_KEY = StringUtil.isNullOrEmpty(get("orderBook.geetest.key")) ? "" : get("orderBook.geetest.key");
    //会员登录  5.1以后使用
    public static final String LOGIN_GEETEST_ID = StringUtils.isBlank(get("login.geetest.id")) ? "" : get("login.geetest.id");
    public static final String LOGIN_GEETEST_KEY = StringUtils.isBlank(get("login.geetest.key")) ? "" : get("login.geetest.key");
    //获取短信验证码
    public static final String SMS_GEETEST_ID = StringUtil.isNullOrEmpty(get("sms.geetest.id")) ? "" : get("sms.geetest.id");
    public static final String SMS_GEETEST_KEY = StringUtil.isNullOrEmpty(get("sms.geetest.key")) ? "" : get("sms.geetest.key");
    //行程确认单短信验证码
    public static final String EC_ITINERARY_GEETEST_ID = StringUtils.isBlank(get("electronicsItinerary.geetest.id")) ? "" : get("electronicsItinerary.geetest.id");
    public static final String EC_ITINERARY_GEETEST_KEY = StringUtil.isNullOrEmpty(get("electronicsItinerary.geetest.key")) ? "" : get("electronicsItinerary.geetest.key");
    //值机选座
    public static final String CHECKIN_SELECT_URL = get("checkin_select_url");
    public static final String GET_DETR_SEG_INFO = "/detr/getDetrSegInfo";
    public static final String SELECT_SEAT_CHART = "/adm/getAdmSeatChart";
    public static final String CHECKIN_RESERVE_SEAT = "/asr/reserveSeat";
    // 选座次数限制
    public static final String CHECKIN_SELECT_SOURCE = get("Juneyao.Visit.Type.CheckIn_Select");
    public static final String CHECKIN_SELECT_SOURCE_CNT = get("Juneyao.Visit.Cnt.CheckIn_Select");
    public static final String CHECKIN_SELECT_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.CheckIn_Select");
    //选座值机未登录次数限制
    public static final String CHECKIN_SELECT_SOURCE_NOT_LOGIN = get("Juneyao.Visit.Type.CheckIn_Select_Not_Login");
    public static final String CHECKIN_SELECT_SOURCE_NOT_LOGIN_CNT = get("Juneyao.Visit.Cnt.CheckIn_Select_Not_Login_Cnt");
    public static final String CHECKIN_SELECT_SOURCE_FOREVER_NOT_LOGIN_CNT = get("Juneyao.Visit.Cnt.Forever.CheckIn_Select_Not_Login_Cnt");
    //问卷调查
    public static final String JUNEYAOAIR_QUESTION_URL = get("juneyaoair.question.url");
    public static final String JUNEYAOAIR_SCORE_URL = get("juneyaoair.score.url");
    public static final String JUNEYAOAIR_QUESTION_GETSURVEY = "/api/Survey/GetSurvey";
    public static final String JUNEYAOAIR_QUESTION_SAVEUSERSURVEYANSWER = "/api/Survey/SaveUserSurveyAnswer";
    // 客票验真次数限制
    public static final String TICKET_VERIFY_SOURCE = get("Juneyao.Visit.Type.Ticket_Verify");
    public static final String TICKET_VERIFY_SOURCE_CNT = get("Juneyao.Visit.Cnt.Ticket_Verify");
    public static final String TICKET_VERIFY_SOURCE_FOREVER_CNT = get("Juneyao.Visit.Cnt.Forever.Ticket_Verify");
    public static final String TICKET_VERIFY_PATH = "/verify/verifyTicket";
    //支付系统
    public static final String EPAY_URL = get("epay.url");
    public static final String EPAY_URL_BANK_QUERY = "/HOYeepayBankBinQuery.aspx";//卡号查询所属银行
    //新值机选座
    public static final String NEW_CHECKIN_SELECT_URL = get("new_checkin_select_url");
    public static final String NEW_CHECKIN_SELECT_CUSTOMER_URL = get("new_checkin_select_customer_url");
    public static final String CUSS_SERVER_URL = get("cuss_server_url");
    public static final String NEW_CHECKIN_SEAT_VERSION = get("new_checkin_seat_version");
    //值机
    public static final String NEW_DO_DETR = "/checkIn/doDetr";//获取当前旅客的所有行程
    public static final String NEW_DO_SYPR = "/checkIn/doSYPR";//获取航班旅客信息
    public static final String NEW_DO_SYPR_ALL = "/checkIn/doSyprAll";//获取当前旅客的所有行程的航班信息
    public static final String NEW_DO_PSR_CHECK_IN = "/checkIn/doPsrCheckin";//单人旅客值机
    public static final String NEW_DO_CANCEL_PSR = "/checkIn/doCancelPsr";//单人旅客值机取消
    public static final String NEW_QUERY_SEAT_MAP = "/checkIn/querySeatMap";//获取航班座位图
    public static final String NEW_QUERY_CHECK_IN_INFO = "/checkIn/queryCheckinInfo";//旅客值机信息查询
    public static final String NEW_QUERY_WEATHER = "/checkIn/queryWeather";//获取天气信息
    public static final String NEW_QUERY_CHECKININFO_BY_MEMBERID = "/checkIn/queryCheckInInfoByMemberId";//根据用户ID旅客值机信息查询
    public static final String NEW_QUERY_CHECKIN_OPEN_TIME = "/checkIn/queryAirportCheckinTime";//值机时间查询
    public static final String GET_BOARDING_PASS = "/ume/getBoardingPass";//获取电子登机数据
    public static final String GET_BOARDING_PASS_STATUS = "/ume/getBoardingPassStatus";//获取电子登机验讫
    public static final String QUERY_TOUR = "/flightTour/queryTour";//查航班行程信息
    public static final String MODIFY_CHECKIN_FREQUENT_INFO = "/checkIn/modifyCheckInFrequentFlyer";//值机添加修改常旅客卡号
    public static final String QUERY_CHECKIN_INFO = "/checkIn/queryCheckinInfo";//旅客值机信息查询
    public static final String VERIFY_FLIGHT_INITIALIZED = "/flightTour/isFlightInitialized";//验证航班初始化
    public static final String QUERY_CHECKIN_COUNT = "/checkIn/queryCheckInCount";//查询值机次数
    //新选座
    public static final String NEW_GET_DETR_SEG_INFO = "/seat/getDetrSegInfo";
    public static final String NEW_SELECT_SEAT_CHART = "/seat/getAdmSeatChart";
    // 查询EMD座位图
    public static final String NEW_EMD_SEAT_MAP = "/seat/querySeatMap/v2";
    // EMD系统预订座位
    public static final String NEW_EMD_RESERVE_SEAT = "/seat/reserveSeat/v2";
    // EMD系统取消选座
    public static final String NEW_EMD_CANCEL_SEAT = "/seat/cancelSeat/v2";
    // EMD系统选座退单
    public static final String NEW_EMD_REFUND_SEAT = "/seat/refundSeatOrder/v2";
    /** 添加同行人 */
    public static final String SEAT_ADD_PEER = "/seat/addPeer";
    public static final String GET_TICKET_INFO = "/ticket/getTicketInfo";
    public static final String GET_FLIGHT_SEAT_STATUS = "/flight/getFlightSeatStatus";
    public static final String GET_TICKET_BASIC_INFO = "/ticket/getTicketBasicInfo";
    // 获取CUSS选座订单信息
    public static final String GET_SEAT_ORDER = "/seat/getSeatOrder";
    // 获取航班登机时间、登记口信息
    public static final String QUERY_FLIGHT_BOARD_IN_AIRPORT = "/customer/travelSky/queryFlightBoardInAirport";
    // EMD系统取消未支付选座订单
    public static final String NEW_EMD_CANCEL_SEAT_ORDER = "/seat/cancelSeatOrder/v2";
    // 查询本地选座信息
    public static final String NEW_GET_LOCAL_SEAT_INFO = "/seat/getLocalSeatInfo/v2";
    /** 获取行程提示信息 */
    public static final String GET_TRAVELLER_TRIP_TIP = "/travellerTrip/getTravellerTripTip";
    public static final String NEW_CHECKIN_RESERVE_SEAT = "/seat/reserveSeat";
    public static final String NEW_CHECKIN_CANCEL_SEAT = "/seat/cancelSeat";
    public static final String GET_IBE_SELECTED_SEAT_INFO = "/seat/getIBESelectedSeatInfos";//获取IBE选座数据
    /** 获取会员相关信息（基于大数据行程接口） */
    public static final String CUSS_MEMBER_GET_MEMBER_INFO = "/member/getMemberInfo";
    public static final String WEB_RECEIVE_INVEREST_PAGE_URL_V3 = "/service/receiveCoupon/index.html";//新版M站领取页面
    public static final String DEFAULT_TOKEN = "www.juneyaoair.com";
    //crm接口地址V3
    public static final String CRM_API_DOMAIN_URL = get("crm_api_domain_url");

    // 会员补登记录查询（Func024）
    public static final String CRM_API_FUNC024 = "/crm/Func024";

    /** 积分交易明细查询 */
    public static final String MILEAGE_DETAIL_QUERY = "/mileageDetail/transactionDetailQuery";
    //积分（固定/滚动）
    public static final String MILEAGEVALIDITY_DETAIL = "/CrmMileage/mileageValidity/detail";

    //旅客服务网接口
    public static final String ID_NAME_CHECK_URL = get("id_name_check_url");
    //pdf文件生成路径
    public static final String PDF_PATH = get("pdf_path");
    //同盾相关配置信息
    public static final String TONGDUN_APIURL = get("tongdun_api_url");
    public static final String TONGDUN_PARTNER_CODE = get("tongdun_partner_code");
    //以下为同盾密钥key
    public static final String TONGDUN_ANDROID_SECRET_KEY = get("android_secret_key");
    public static final String TONGDUN_IOS_SECRET_KEY = get("ios_secret_key");
    public static final String TONGDUN_HARMONY_SECRET_KEY = get("harmony_secret_key");
    public static final String TONGDUN_WEB_SECRET_KEY = get("web_secret_key");
    public static final String TONGDUN_XCX_SECRET_KEY = get("xcx_secret_key");
    public static final String TONGDUN_ZFB_SECRET_KEY = get("zfb_secret_key");
    //同盾事件配置
    //安卓
    public static final String TONGDUN_ANDROID_LOGIN_EVENT_ID = get("tongdun_android_login_event_id");
    public static final String TONGDUN_ANDROID_REGISTER_EVENT_ID = get("tongdun_android_register_event_id");
    public static final String TONGDUN_ANDROID_TRADE_EVENT_ID = get("tongdun_android_trade_event_id");
    public static final String TONGDUN_ANDROID_CHECKIN_LOOKUP_EVENT_ID = get("tongdun_android_checkin_lookup_event_id");
    public static final String TONGDUN_ANDROID_MODIFY_SALEPWD_EVENT_ID = get("tongdun_android_modify_salepwd_event_id");
    public static final String TONGDUN_ANDROID_USE_SCORE_EVENT_ID = get("tongdun_android_use_score_event_id");
    public static final String TONGDUN_ANDROID_LOOKUP_EVENT_ID = get("tongdun_android_lookup_event_id");
    public static final String TONGDUN_ANDROID_MARKETING_EVENT_ID = get("tongdun_android_marketing_event_id");
    public static final String TONGDUN_ANDROID_RESERVE_EVENT_ID = get("tongdun_android_reserve_event_id");
    public static final String TONGDUN_ANDROID_PAYMENT_EVENT_ID = get("tongdun_android_payment_event_id");
    public static final String TONGDUN_ANDROID_SMS_EVENT_ID = get("tongdun_android_sms_event_id");
    //ios
    public static final String TONGDUN_IOS_LOGIN_EVENT_ID = get("tongdun_ios_login_event_id");
    public static final String TONGDUN_IOS_REGISTER_EVENT_ID = get("tongdun_ios_register_event_id");
    public static final String TONGDUN_IOS_TRADE_EVENT_ID = get("tongdun_ios_trade_event_id");
    public static final String TONGDUN_IOS_LOOKUP_EVENT_ID = get("tongdun_ios_lookup_event_id");
    public static final String TONGDUN_IOS_CHECKIN_LOOKUP_EVENT_ID = get("tongdun_ios_checkin_lookup_event_id");
    public static final String TONGDUN_IOS_MARKETING_EVENT_ID = get("tongdun_ios_marketing_event_id");
    public static final String TONGDUN_IOS_RESERVE_EVENT_ID = get("tongdun_ios_reserve_event_id");
    public static final String TONGDUN_IOS_PAYMENT_EVENT_ID = get("tongdun_ios_payment_event_id");
    public static final String TONGDUN_IOS_SMS_EVENT_ID = get("tongdun_ios_sms_event_id");
    public static final String TONGDUN_IOS_MODIFY_SALEPWD_EVENT_ID = get("tongdun_ios_modify_salepwd_event_id");
    public static final String TONGDUN_IOS_USE_SCORE_EVENT_ID = get("tongdun_ios_use_score_event_id");
    //鸿蒙
    public static final String TONGDUN_HARMONY_LOGIN_EVENT_ID = get("tongdun_harmony_login_event_id");
    public static final String TONGDUN_HARMONY_REGISTER_EVENT_ID = get("tongdun_harmony_register_event_id");
    public static final String TONGDUN_HARMONY_TRADE_EVENT_ID = get("tongdun_harmony_trade_event_id");
    public static final String TONGDUN_HARMONY_CHECKIN_LOOKUP_EVENT_ID = get("tongdun_harmony_checkin_lookup_event_id");
    public static final String TONGDUN_HARMONY_MODIFY_SALEPWD_EVENT_ID = get("tongdun_harmony_modify_salepwd_event_id");
    public static final String TONGDUN_HARMONY_USE_SCORE_EVENT_ID = get("tongdun_harmony_use_score_event_id");
    public static final String TONGDUN_HARMONY_LOOKUP_EVENT_ID = get("tongdun_harmony_lookup_event_id");
    public static final String TONGDUN_HARMONY_MARKETING_EVENT_ID = get("tongdun_harmony_marketing_event_id");
    public static final String TONGDUN_HARMONY_RESERVE_EVENT_ID = get("tongdun_harmony_reserve_event_id");
    public static final String TONGDUN_HARMONY_PAYMENT_EVENT_ID = get("tongdun_harmony_payment_event_id");
    public static final String TONGDUN_HARMONY_SMS_EVENT_ID = get("tongdun_harmony_sms_event_id");
    //h5
    public static final String TONGDUN_WEB_LOGIN_EVENT_ID = get("tongdun_web_login_event_id");
    public static final String TONGDUN_WEB_REGISTER_EVENT_ID = get("tongdun_web_register_event_id");
    public static final String TONGDUN_WEB_TRADE_EVENT_ID = get("tongdun_web_trade_event_id");
    public static final String TONGDUN_H5_LOOKUP_EVENT_ID = get("tongdun_h5_lookup_event_id");
    public static final String TONGDUN_H5_CHECKIN_LOOKUP_EVENT_ID = get("tongdun_h5_checkin_lookup_event_id");
    public static final String TONGDUN_H5_MARKETING_EVENT_ID = get("tongdun_h5_marketing_event_id");
    public static final String TONGDUN_H5_RESERVE_EVENT_ID = get("tongdun_h5_reserve_event_id");
    public static final String TONGDUN_H5_PAYMENT_EVENT_ID = get("tongdun_h5_payment_event_id");
    public static final String TONGDUN_H5_SMS_EVENT_ID = get("tongdun_h5_sms_event_id");
    public static final String TONGDUN_H5_MODIFY_SALEPWD_EVENT_ID = get("tongdun_h5_modify_salepwd_event_id");
    public static final String TONGDUN_H5_USE_SCORE_EVENT_ID = get("tongdun_h5_use_score_event_id");

    //小程序
    public static final String TONGDUN_XCX_LOGIN_EVENT_ID = get("tongdun_xcx_login_event_id");
    public static final String TONGDUN_XCX_TRADE_EVENT_ID = get("tongdun_xcx_trade_event_id");
    public static final String TONGDUN_XCX_LOOKUP_EVENT_ID = get("tongdun_xcx_lookup_event_id");
    public static final String TONGDUN_XCX_CHECKIN_LOOKUP_EVENT_ID = get("tongdun_xcx_checkin_lookup_event_id");
    public static final String TONGDUN_XCX_MARKETING_EVENT_ID = get("tongdun_xcx_marketing_event_id");
    public static final String TONGDUN_XCX_RESERVE_EVENT_ID = get("tongdun_xcx_reserve_event_id");
    public static final String TONGDUN_XCX_PAYMENT_EVENT_ID = get("tongdun_xcx_payment_event_id");
    public static final String TONGDUN_XCX_SMS_EVENT_ID = get("tongdun_xcx_sms_event_id");
    public static final String TONGDUN_XCX_MODIFY_SALEPWD_EVENT_ID = get("tongdun_xcx_modify_salepwd_event_id");
    public static final String TONGDUN_XCX_USE_SCORE_EVENT_ID = get("tongdun_xcx_use_score_event_id");
    //支付宝小程序
    public static final String TONGDUN_ZFB_MODIFY_SALEPWD_EVENT_ID = get("tongdun_zfb_modify_salepwd_event_id");
    public static final String TONGDUN_ZFB_CHECKIN_LOOKUP_EVENT_ID = get("tongdun_zfb_checkin_lookup_event_id");
    public static final String TONGDUN_ZFB_USE_SCORE_EVENT_ID = get("tongdun_zfb_use_score_event_id");
    public static final String TONGDUN_ZFB_SMS_EVENT_ID = get("tongdun_zfb_sms_event_id");


    //订单系统（产品管理系统）
    public static final String URL_FARE_API = get("UnitOrder.NewAPI.URL");
    public static final String COUPON_QUERY_PRODUCT_V2 = "/Order/QueryCouponProduct"; //可售产品查询
    public static final String COUPON_BUY_PRODUCT_V2 = "/Order/BuyCouponProduct"; //可售产品购买
    public static final String COUPON_CHECK_PRODUCT_V2 = "/Order/UseCheckCouponProduct";//权益使用检验
    public static final String COUPON_MY_PRODUCT_V2 = "/Order/GetMyCouponProduct";//我的权益券查看
    public static final String QUERY_MY_PRODUCT_COUPON_COUNT = "/Order/QueryMyProductCouponCount";//查询权益劵/卡数量

    public static final String ACCOUNT_BIND= "/ThemeCoupon/themeCouponNumberOfAccountBind";//API账户是否还可绑定主题卡
    public static final String COUPON_PRO_LIMIT= "/Order/LimitCouponProduct";//资源适配航线航班(产品管理系统)
    public static final String PRODUCT_ADDRESS_PICKUP= "/Order/QueryPickUpAddress";//查询wifi电话卡取件地址
    public static final String COUPON_VISA_DICT = "/Order/CouponProductVisaDictionaries";//签证相关类型字典码获取
    public static final String COUPON_SEND_OUT = "/Order/SendOutCouponProduct";//任务中心积分、优惠券、权益券领取 2021-03-15
    public static final String PAY_ORDER_SCROE_SHARE = "/Pay/ScroeShare";//积分后置-积分分摊
    public static final String PAY_SCORE_CONTROL_SWITCH = "/Pay/ScoreControlSwitch";//积分后置控制开关接口
    public static final String QUERY_OWN_SALE= "/Order/QueryOwnSale"; //自营客票判断
    public static final String CHANGE_COUPON_CREATE_ORDER = "/Order/RescheduleCoupon/CreateCouponOrder/v10";
    public static final String PROTOCOL_QUERY_RESOURCE = "/Order/Default/QueryProducts/v10"; //2020-06-18 礼宾引导 初始化礼宾产品资源信息
    public static final String PROTOCOL_QUERY_PRODUCT = "/Order/GuideService/QueryProducts/v10"; //2020-05-25 礼宾引导 查询产品
    public static final String PROTOCOL_QUERY_SERVICE_CHECK = "/Order/GuideService/ServiceCheck/v10"; //2020-05-29 校验机场是否开通此服务
    public static final String PROTOCOL_CREATE_PRODUCT = "/Order/GuideService/CreateCouponOrder/v10"; //2020-05-25 礼宾引导 创建订单
    public static final String QUERY_UNBINDING_RECORD="/Order/UpgradeUnlimited/QueryUnBindingRecord";//查询用户是否存在未绑定得升舱卡
    public static final String QUERY_UNLIMITED_UPCLASS_ORDER = "/Order/QueryUpgradeCardOrderCount";// 查询无限升舱卡升舱的未出行航段数量
    public static final String QUERY_UNLIMITED_FLY_ORDER = "/Order/QueryUnlimitFlyNoUseCount";// 查询畅飞卡的未出行航段数量
    public static final String QUERY_PRODUCT_V10 = "/Order/Comment/QueryProducts/v10";//查询产品信息
    public static final String PRODUCT_QUERY_V10 = "/Order/${couponSource}/QueryProducts/v10";//主题卡查询
    public static final String QUERY_THEME_COUPON = "/ThemeCoupon/queryEffectiveOfThemeCoupon";//查询产品信息

    public static final String QUERY_ACTIVITY_INFO = "/Order/queryActivityInfo";//查询优惠券or权益券活动info

    public static final String QUERY_FLIGHT_BY_COUPON = "/Shop/QueryFlightByCoupon";

    public static final String REDEEM_BINDING_LIST = "/Order/themeCard/redeemBindingList";//查询产品信息


    public static final String QUERY_TICKET_REDEEM = "/Order/queryTicketRedeem";//

    public static final String PRODUCT_CREATE_V10 = "/Order/Comment/CreateCouponOrder/v10";//产品下单  2020-09-25
    public static final String PRODUCT_CREATE = "/Order/${resourceType}/CreateCouponOrder/v10";//通用的下单链接
    public static final String QUERY_UNLIMITED_FLY_BING_RECORD = "/Order/UnlimitedFly/QueryFlyCardBindingRecord";//查询儿童畅飞卡绑定记录
    public static final String QUERY_UNLIMITED_FLY_BING_RECORD_V2 = "/Order/UnlimitedFly/QueryFlyCardBindingRecordV2";//查询畅飞卡绑定记录 2020-11-17
    public static final String QUERY_ORDER_PAYSTATEBY_COUPONCODE = "/Order/QueryOrderPayStateByCouponCode";//根据主题卡券码查询查询兑换机票订单状态
    public static final String UNLIMITED_FLY_BINDING = "/Order/UnlimitedFly/Binding";//儿童畅飞卡绑定
    public static final String QUERY_UNBINDINFO = "/Order/UnlimitedFly/QueryUnBindInfo";//查询是否存在未绑定儿童畅飞卡,吉祥畅飞卡
    public static final String QUERY_UNBINDINFO_V2 = "/Order/UnlimitedFly/QueryUnBindInfoV2";//查询是否有未绑定畅飞卡
    public static final String ADTFLY_BIND = "/Order/Binding";//绑定吉祥畅飞卡
    public static final String QUERY_PURCHASABLE_QUANTITY="/Order/UnlimitedFly/QueryRemnantQuantity"; //查询产品可够数量和是否含有可用库存 2020-11-25
    public static final String QUERY_PASSENGER_BT_TICKET_NO="/Order/QueryPassengersByTicketNo"; //根据票号查询订单中乘客信息
    public static final String CREATE_GENTINY_URL="/Tiny/genTinyUrl";//长链接转短链接

    //预留登机牌相关
    //查询航班限额
    public static final String BOARDING_PRODUCT_LIMIT = "/Order/CheckinSubstitution/QueryProductLimit/v10";
    public static final String BOARDING_PRODUCT_BUY = "/Order/CheckinSubstitution/CreateCouponOrder/v10";
    public static final String BOARDING_PRODUCT_DETAIL = "/Order/CheckinSubstitution/GetCouponOrderDetail/v10";
    public static final String BOARDING_PRODUCT_CANCEL = "/Order/CheckinSubstitution/CancelApplyCouponOrder/v10";
    //登陆token,token中负载保存的数据
    public static final String TOKEN_CHANNELCODE = "channelCode";
    public static final String TOKEN_MEMBERCARDNO = "memberCardNo";
    public static final String WX_OPENID = "openid";
    public static final String USER_ID= "userId";
    public static final String TOKEN_CRMTOKEN= "crmToken";
    public static final String TOKEN_MEMBERINFO = "memberInfo";
    public static final String CLIENT_VERSION = "clientVersion";
    public static final String CLIENT_VERSION_CODE = "versionCode";
    public static final String CLIENT_PLATFORM = "platforminfo";
    public static final String BLACKBOX_FROM = "from";
    public static final String CLIENT_USERAGENT= "user-agent";
    public static final String CLIENT_COOKIE= "cookie";
    public static final String CLIENT_HEADER_OPENID= "openId";
    //移动端token保存用户信息
    public static final String MOBILE_TOKEN_USER_INFO= "mobileTokenUserInfo";
    //HANDLE地址
    public static final String HANDLE_URL = get("handle.url");

    //表扬,投诉,意见新增接口
    public static final String SPR_SUG_COM_B2C = "/quality/qosPsimSuggest/addPriSugComB2C";
    public static final String CREATE_SUGGESTION = "/Activity/CreateFeedback";

    public static final String PAY_CANCEL_SELECTSEAT = "/Order/RefundSeatProduct";//取消付费选座
    public static final String MODIFY_SEAT_FLYER = "/Order/ModifySeatFlyer";//选座-修改常旅客

    //旅客服务网
    public static final String CMS_URL = get("cms.url");
    //基础服务相关API
    public static final String BASIC_INFO_VERSION = "1.0";
    public static final String BASIC_INFO_URL = get("basic.info.url");
    public static final String BASIC_PROVIDER_URL = get("basic.provider.url");
    public static final String BASIC_INFO_FLIGHTINFO = "/flightbasic/basic/flightInfoList";//航班基础信息查询
    public static final String BASIC_INFO_AIRLINES = "/flightbasic/basic/airLines";//航线信息查询
    public static final String BASIC_INFO_COMMONCITY_QUERY = "/flightbasic/basic/queryCommonFlyCityDetailsByCityCode";//查询共飞城市
    public static final String BASIC_INFO_PICTURELIST = "/flightbasic/indexHandle/getPictureList";//广告位
    public static final String BASIC_INFO_AGGREGATE = "/flightbasic/indexHandle/fetchAggregatePageAV";//聚合页广告位查询
    public static final String BASIC_INFO_HOTEL_ADVERTISEMENT = "/flightbasic/hotelProducts/queryHotelProducts";//酒店广告
    public static final String BASIC_INFO_SERVICES = "/flightbasic/indexHandle/getModulars";//首页服务配置
    public static final String BASIC_INFO_MESSAGE = "/flightbasic/indexHandle/getMessageList";//公告查询
    public static final String BASIC_INFO_MESSAGE_V2 = "/flightbasic/indexHandle/fetchMessageList";//公告查询V2.0
    public static final String BASIC_INFO_MESSAGE_DETAIL = "/flightbasic/indexHandle/getMessgeDetail";//公告详情查询
    public static final String BASIC_INFO_SPLASH_PATH = "/flightbasic/indexHandle/getAppPictureList";//APP启动屏
    public static final String BASIC_INFO_APP_VER = "/flightbasic/indexHandle/getAppVersion";//APP版本
    public static final String BASIC_INFO_ZIP_VER = "/flightbasic/indexHandle/compareVer";//zip资源包管理
    public static final String BASIC_INFO_SPECIALAIRLINE = "/flightbasic/indexHandle/getSpecialAirLineList";//特价航线
    public static final String BASIC_INFO_CUSTON_PIC = "/flightbasic/indexHandle/getCustomPictureList";//自定义广告位
    public static final String BASIC_INFO_COUNTRYS= "/flightbasic/basic/countrys";//国家信息查询
    public static final String ATTENTION_FLIGHT_AIRLINE = "/flightbasic/followAirLine/queryFollowAirLine";//查询航班关注情况
    public static final String ATTENTION_CANCEL_AIRLINE = "/flightbasic/followAirLine/cancelAirline";//取消关注

    public static final String ROUTE_CALENDAR_LIST = "/flightbasic/routeCalendar/routeCalendarList";//航线兑换日历
    //查询关注航班列表
    public static final String ATTENTION_LiST_FLIGHT = "/attentionFlight/queryAttentionFlightList";
    //增加航班关注
    public static final String ATTENTION_ADD_FLIGHT = "/attentionFlight/addAttentionFlight";
    //取消航班关注
    public static final String SEARCH_MEMBER_RIGHT_URL = "/flightbasic/memberCenter/searchMemberRight";//查询会员权益
    public static final String POLICY_NOTICE_INFO_URL = "/flightbasic/indexHandle/getAllNoticeInfo";//查询条款服务
    public static final String POLICY_NOTICE_TEXT_URL = "/flightbasic/indexHandle/getRichTextNoticeInfo";//查询条款富文本服务
    public static final String GATE_UPGRADE_QUERY_RULES = "/flightbasic/upgradeManage/queryUpgradeRule";//查询登机口升舱产品规则
    public static final String BASIC_API_AIRPORTS_QUERY = "/flightbasic/api/queryAirports";//API机场查询
    public static final String BASIC_INFO_GETNAVIGATION = "/flightbasic/indexHandle/getNavigation";//导航栏
    public static final String BASIC_WALLET_QUERYWALLETDETAIL = "/flightbasic/wallet/queryWalletDetail";//查询钱包
    public static final String BASIC_API_CITYS_QUERY = "/api/queryCity";//API城市查询
    //API航线查询
    public static final String BASIC_API_AIRLINES_QUERY = "/b2cbasic/api/queryMatchAirline";
    //积分商城商品查询
    public static final String BASIC_MALLSHOPPRODUCT_QUERY ="/b2cbasic/mallShopProduct/selectList";
    public static final String BASIC_MALLSHOPPRODUCT_QUERY_V2 ="/b2cbasic/mallShopProduct/queryHotelProductsV2";

    public static final String QUERY_PAY_METHOD_INFO = "/b2cbasic/payMethod/searchPayMethods";//查询支付方式信息

    public static final String PRODUCT_QUERY_AVAILABLE = "/Product/productQueryAvailable";
    public static final String COUPON_USE_CUSTOM_COUPON= "/Coupon/UseCustomCoupon";
    /**
     * 机场信息查询
     */
    public static final String BASIC_INFO_AIRPORTS = "/flightbasic/basic/airPortInfosV2";


    public static final String BASIC_INFO_AIRPORT = "/flightbasic/basic/airPortInfos";

    /**
     * 航距查询
     */
    public static final String BASIC_FLIGHT_DISTANCE = "/basic/flightDistance/query";


    /**
     * 联名信用卡信息查询
     */
    public static final String BASIC_CO_BRAND_CREDIT_AD = "/indexHandle/coBrandCreditCardAd";


    /** 基础服务航距查询 */
    public static final String FLIGHT_BASE_AIRLINE_MILEAGE = "/airline/mileage";

    public static final String QUERY_COUPON_GRANT = "/activity/couponGrant/queryCouponGrant";// 查询可领取优惠券
    public static final String SAVE_COUPON_GRANT_RECORD = "/activity/couponGrant/couponReceive";// 记录领取优惠券记录
    public static final String QUERY_TASK_STATUS = "/activity/task/queryTaskStatus";// 任务中心  查询会员任务中心开启状态 2021-03-12
    public static final String QUERY_TASK_LIST = "/activity/task/queryTask";// 任务中心 任务列表查询 2021-03-12
    public static final String UPDATE_TASK_STATUS = "/activity/task/updateTaskStatus";// 任务中心 更新会员任务中心开启状态 2021-03-15
    public static final String UPDATE_TASK_MEMBER_INFO = "/activity/task/updateTaskMemberInfo";// 任务中心 更新会员任务状态 2021-03-12
    public static final String CHECK_TASK_MEMBER_STATUS = "/activity/task/checkTaskMemberStatus";// 任务中心 校验会员任务状态 2021-03-12
    public static final String QUERY_PREMIUM_BY_CHANEL_CODE = "/activity/activityPremium/findByChanelCode";//权益服务查询
    public static final String  QUERY_COUPON_GRANT_BY_TYPEANDCHANNELCODE = "/activity/couponGrant/queryCouponGrantByTypeAndChannelCode";// 查询赠送优惠券活动
    public static final String QUERY_COUPON_GRANT_RECEIVE_COUNT = "/activity/couponGrant/queryReceiveCount";// 查询参与活动次数
    public static final String RECEIVE_COUPON_GRANT_RECEIVE = "/activity/couponGrant/avFareCouponReceive";// 保存领取优惠券信息

    public static final String BASIC_MPSCENEQRCODE_INSERTRECORD = "/activity/mpSceneQrCode/insertRecord";//保存微信，支付宝小程序实名认证用户信息
    public static final String BASIC_INFO_DATA_VER = "/indexHandle/getDataVersionList";//数据版本信息
    /**
     * 根据三字码以及时间查询航班信息
     */
    public static final String BASIC_FLIGHTINFO_QUERY_BY_CITY_AND_DATES= "/basic/getFlightByCityAndDate";
    /**
     * 查询指定城市是否有航班信息
     */
    public static final String EXIST_FLIGHTINFO_QUERY_BY_CITY_AND_DATES= "/basic/existFlightByCityAndDate";
    /**
     * 根据航班基础信息查询航班信息
     */
    public static final String BASIC_FLIGHTINFO_QUERY = "/basic/flightInfoList";

    /**
     * 风险判断
     */
    public static final String RISK_ANTIFRAUD = "/flightbasic/risk/antifraud";
    //城市主题查询
    public static final String CITY_THEME = "/flightbasic/CityThemeManage/queryCityTheme";

    // 吉祥语相关
    public static final String GEN_SHORT_SENTENCE = "/activity/shortsentence/genShortSentence";//生成吉祥语
    public static final String QUERY_SHORT_SENTENCE = "/activity/shortsentence/queryShortSentence";//查询吉祥语
    public static final String QUERY_ALL_SHORT_SENTENCE_TAG = "/activity/shortsentence/queryAllTags";//查询所有吉祥语标签
    public static final String QUERY_FLIGHT_YPRICE = "/b2cbasic/basic/searchFlightTicketPriceByYAndCity";// 查询航班Y舱运价
    /** 查询APP消息清单 */
    public static final String GET_PUSH_DETAIL_LIST = "/pushDetail/getPushDetailList";
    /** 获取未读消息的数量 */
    public static final String GET_NO_READ_NUM = "/pushDetail/getNoReadNum";
    /** 更新消息状态 */
    public static final String UPDATE_STATE = "/pushDetail/updateState";

    //普通预定邮箱配置
    /**
     * 邮件HOSTNAME
     */
    public static final String EMAIL_HOST_NAME = get("email.hostname");
    /**
     * 邮件用户名
     */
    public static final String EMAIL_USER_NAME = get("email.username");
    /**
     * 发件人名称
     */
    public static final String EMAIL_SENDER_NAME = "吉祥航空";

    /**
     * 深港通产品常量
     */
    public static final String BUS_FARE = "JOWABUS";

    public static final String BUS_PLUS = "BUSPLUS";

    //电子发票邮箱配置
    /**
     * 电子发票发送邮件专用用户(暂不可用)
     */
    public static final String INVOICE_EMAIL_USER_NAME = get("invoice.email.username");

    /**
     * 电子发票发送邮件专用用户(暂不可用)
     */
    public static final String INVOICE_EMAIL_PASSWORD = get("invoice.email.password");

    public static final String LIVE_BODY_CHECK_FACE_URL = get("livebody.checkface.url");

    public static final String LIVE_BODY_USER_NAME = get("livebody.user.name");

    public static final String LIVE_BODY_USER_PWD = get("livebody.user.pwd");

    public static final String MEMBER_CENTER_URL = get("member.center.url");

    public static final String ACTIVITY_QUERY_TESSERA = "/activityService/queryTessera";

    public static final String GATE_UPGRADE_QUERY_UPG_TOURS = "/Order/GateUpgrade/QueryUpgTours/v10";

    public static final String GATE_UPGRADE_GET_ORDER_INFO = "/Order/GateUpgrade/GetOrderInfo/v10";

    public static final String GATE_UPGRADE_QUERY_UPG_SEAT_MAP = "/Order/GateUpgrade/QueryUpgSeatMap/v10";

    public static final String GATE_UPGRADE_CREATE_ORDER = "/Order/GateUpgrade/CreateOrder/v10";

    public static final String GATE_UPGRADE_CANCEL_ORDER = "/Order/GateUpgrade/CancelOrder/v10";

    public static final String TRANSFER_ACCOMMODATION_APPLY_URL = "/Order/TransferHouse/CreateCouponOrder/v10";

    public static final String TRANSFER_ACCOMMODATION_CHECK_APPLY_URL = "/Order/TransferHouse/CheckHavingApplyOrNot/v10";

    public static final String TRANSFER_ACCOMMODATION_QUERY_ORDER_DETAIL_URL = "/Order/TransferHouse/GetCouponOrderDetail/v10";



    public static final String APP_ID = get("weixin.app.id");

    public static final String secret = get("weixin.secret");

    public static final String MWEB_CHANNEL_CODE = get("UnitOrder.WebAPI.MWEB.CHANNEL_CODE");


    //产品管理 2019-12-12
    //权益券查询地址
    public static final String URL_COUPON_API = get("Product.Coupon.URL");
    public static final String COUPON_QUERY= "/QueryCouponProduct/queryProduct"; //权益券
    public static final String RIGHTS_COUPON_QUERY= "/v2/QueryCouponProduct/queryProductResourceInfo"; //权益券使用规则查询

    //Live800在线客服
    public static final String URL_LIVE800 = get("url_Live800");
    public static final String KEY_LIVE800 = get("key_Live800");
    public static final String COMPANY_ID_LIVE800 = get("companyID");
    public static final String CONFIG_ID_LIVE800 = get("configID");
    public static final String JID_LIVE800 = get("jid");

    /**
     * 腾讯云相关服务
     */
    public static final String TENCENT_CLOUD_VERSION = "1.0.0";
    public static final String CLOUD_TENCENT_SERVER = get("cloud.tencent.server");
    public static final String CLOUD_TENCENT_GETFACEID = "/api/server/getfaceid";
    public static final String CLOUD_TENCENT_SYNC = "/api/server/sync";
    /**
     *人脸核身结果查询接口(升级) 替换/api/server/sync服务
     * https://cloud.tencent.com/document/product/1007/35880
     */
    public static final String QUERY_FACE_RECORD = "/api/v2/base/queryfacerecord";
    public static final String CLOUD_TENCENT_NONCE_TICKET = "/api/oauth2/api_ticket";//获取 NONCE ticket

    //crm java微服务 2020-03-18
    public static final String SEGMENT_QUERY= "/mileageDetail/currentSegmentQuery"; //会员航段查询
    public static final String FLIGHT_ACTIVITY_QUERY= "/mileageDetail/flightActivityQuery"; //航空活动查询（查询近一年是否有乘机记录） 2020-09-09
    public static final String WHITELIST_QUERY= "/CrmThirdparty/whiteListQuery"; //先飞后付白名单查询
    /**
     * 非航积分累积
     */
    public static final String SEND_SENDMILEAGE_SM2 = "/CrmMileage/mileage/sendMileage/sm2";
    public static final String STUDENT_AUTH_DETAIL_QUERY= "/getStudentCertificateValidateResult"; //学生认证 获取验证结果详情 2020-05-08
    public static final String STUDENT_AUTH_SUBMIT= "/CrmMember/submitStudentCertificateValidateInfo"; //学生认证信息提交  2020-05-09

    public static final String COMMON_CONTACT_QUERY= "/linkman/queryAll"; //查询常用联系人  2020-05-12
    public static final String COMMON_CONTACT_SUBMIT= "/linkman/insert"; //添加常用联系人  2020-05-12
    public static final String COMMON_CONTACT_UPDATE= "/linkman/updateByPri"; //修改常用联系人  2020-05-12
    public static final String COMMON_CONTACT_DELETE= "/linkman/deleteByPri"; //删除单个常用联系人  2020-05-12
    public static final String COMMON_CONTACT_DELETE_BATCH= "/linkman/deleteBatch"; //批量删除常用联系人  2020-05-12
    //http://172.22.0.35:8013 不带网关的crm测试环境
    public static final String COMPANY_MEMBER_VERIFY= "/CrmMember/companyMember/verify"; //企业会员验证  2020-06-19
    public static final String COMPANY_MEMBER_BIND= "/CrmMember/companyMember/bind"; //企业会员激活  2020-06-19
    public static final String COMPANY_MEMBER_UNBOUND= "/CrmMember/companyMember/unbound"; //企业会员解绑  2020-06-19
    public static final String COMPANY_MEMBER_SENDEMAIL= "/companyMember/sendEmail"; //企业会员激活邮件发送  2020-06-19
    public static final String COMPANY_MEMBER_QUERYINFO= "/companyMember/queryInfo"; //企业会员信息查询  2020-06-19
    public static final String COMPANY_MEMBER_GROUPAPPLY= "/CrmMember/companyMember/groupApply"; //企业会员团队出行预订申请  2021-02-24
    /**
     * 会员是否有等级变动查询接口
     */
    public static final String QUERY_MEMBERLEVEL_LEVELCHANGE  = "/CrmMember/memberLevel/memberLevelChangeHisQuery";
    public static final String COMPANY_MEMBER_QUERYCOMPANYNAME= "/company/queryCompanyName"; //企业会员验证邮箱名称  2021-11-24
    public static final String UNLIMITED_UP_APPLY = "/Activity/ApplyAppointment";  //无限升舱卡 预约申请 2020-07-03
    public static final String UNLIMITED_UP_BIND = "/Order/UpgradeUnlimited/Binding";  //无限升舱卡 绑定 2020-07-05
    public static final String UNLIMITED_UP_QUERYBIND = "/Order/UpgradeUnlimited/QueryBindingInfo";  //无限升舱卡 查看绑定信息 2020-07-05

    public static final String BENEFICIARY_QUERY = "/beneficiary/queryByCriteria"; //查询受益人（草稿-M，激活-A，删除-D）  2020-07-30
    public static final String BENEFICIARY_ADD = "/beneficiary/add"; //受益人新增  2020-07-31
    public static final String BENEFICIARY_ACTIVE = "/beneficiary/active"; //受益人激活  2020-07-31
    public static final String BENEFICIARY_DELETE = "/beneficiary/delete"; //受益人删除  2020-07-31
    public static final String BENEFICIARY_MODIFY = "/beneficiary/update"; //草稿受益人修改 2020-07-31
    public static final String BENEFICIARY_QUERY_BY_MEMBERID = "/beneficiary/queryByMemberId"; // 通过会员id查询各类别数量 2020-07-31
    public static final String MEMBER_ACCOUNT_FORCE_ADD_IDCARD = "/memberCertificate/forceAddIdCard";   //强制添加会员证件  2020-08-31
    // 订单系系统 常用报销凭证
    public static final String COMMON_INVOICE_GET = "/Book/GetGeneralInvoiceById"; //查询常用报销凭证 2020-11-12
    public static final String COMMON_INVOICE_QUERY = "/Book/QueryGeneralInvoice"; //查询常用报销凭证 2020-11-12
    public static final String COMMON_INVOICE_ADD = "/Book/AddGeneralInvoice"; //添加常用报销凭证 2020-11-12
    public static final String COMMON_INVOICE_MODIFY = "/Book/ModifyGeneralInvoice"; //修改常用报销凭证 2020-11-12

    public static final String DISNEYTICKET_CHECKTICKETSTATUS = "/Service/DisneyTicket/CheckTicketStatus";
    public static final String COMMON_INVOICE_DELETE = "/Book/DelGeneralInvoice"; //删除常用报销凭证 2020-11-12
    public static final String COUPON_DISNEY_PRICE_CALENDAR = "/Book/DisneyPriceCalendar";
    public static final String DISNEY_TICKET_CHANGE = "/Order/DisneyTicketChange";  //迪士尼优选服务改期
    public static final String SPECIAL_REFUND_APPLY = "/Activity/RefundApply";  //特殊退票申请 2021-01-18

    //订单系统品牌权益运价查询
    public static final String QUERY_BRAND_RIGHT = "/BrandRight/query";  //品牌权益运价查询 2021-05-11
    public static final String ORDER_CANCEL_COUPONORDER = "/Order/BasicCancelCouponOrder";  //预付费行李取消订单 2021-08-12
    public static final String ORDER_REFUND_COUPONORDER = "/Order/BasicRefundCouponOrder";  //预付费行李退款 2021-08-12
    public static final String ORDER_GET_COUPONOINFO = "/Order/BasicGetCouponInfo";  //预付费行李订单详情 2021-08-12


    // 产品系统
    public static final String PRODUCT_PREPAYBAGGAGE_ORDERINFO = "/basicOrder/queryOrderInfo";  //查询预付费行李订单信息
    public static final String QUERY_PREPAYBAGGAGE_CREATEORDER = "/basicOrder/create";  //预付费行李下单 2021-08-12
    public static final String QUERY_PREPAYBAGGAGE_PRICE = "/v2/smngAvailableP/findAvailableP";  //查询预付费行李价格 2021-08-11
    public static final String PRODUCT_CHECK_CANPAY_BAGGAGE = "/v2/smngAvailableP/judgeExtraPro";  //预付费行李查询航段对应产品是否存在 2021-08-12
    public static final String PRODUCT_QUERY_RESOURCE = "/v2/smngAvailableP/queryResource";
    //调用畅飞类型
    public static final String PRODUCT_THEME_COUPON_ACCOUNTR_EDEEM_INFO = "/ThemeCoupon/themeCouponAccountRedeemInfo";
    //查询权益卷总数
    public static final String QUERY_VOUCHER_COUNT = "/InquiryVoucherController/InquiryVoucherCount";

    public static final String BUSINESS_TYPE_INVOLUNTARY = "INVOLUNTARY"; //INVOLUNTARY

    public static final String BUSINESS_TYPE_DELAYPROVE = "DELAYPROVE"; //DELAYPROVE

    public static final String QUERY_CANCEL_ORDER = "/ho-cc/cancelTicketSeat/queryCancelSeat";  //查询取消订座列表信息 2021-08-03
    public static final String QUERY_CANCEL_PNR = "/ho-cc/cancelTicketSeat/cancelSeat";  //查询取消PNR 2021-08-03
    public static final String QUERY_CANCEL_PNR_Detail = "/ho-cc/cancelTicketSeat/cancelHistory";  //查询取消PNR详情 2021-08-23
    //wallet相关配置
    public static final String WALLET_APPLE_WWDRCA = get("wallet.apple.wwdrca");
    public static final String WALLET_KEYSTORE_PATH = get("wallet.keystore.path");
    public static final String WALLET_KEYSTORE_PASSWORD = get("wallet.keystore.password");
    public static final String WALLET_MEMBER_TEMPLATE = get("wallete.member.template");
    public static final String WALLET_COMMON_MEMBER_ICON = "passkit/member/common";
    public static final String WALLET_BLACK_MEMBER_ICON = "passkit/member/black";

    //极验一键登录
    public static final String GEETEST_ONELOGIN_URL = get("geetest.onelogin.url");
    public static final String GEETEST_ONELOGIN_APPID = get("geetest.onelogin.appid");
    public static final String GEETEST_ONELOGIN_APPKEY = get("geetest.onelogin.appkey");

    /**
     * 多程订单类型
     */
    public static final String ORDER_TICKET_RANGE_TYPE_MULTIPLE = "MultiRange";

    /**
     * 苹果登录 私钥
     */
    public static final String APPLE_LOGIN_PRIVATE_KEY = "MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQg4eoWp0hAtDW8V3A7\n" +
            "gRINhaRuF+hszAfoq9fYrnFUl/igCgYIKoZIzj0DAQehRANCAARz8HU/Xwt8FRlA\n" +
            "eXepmfWxCglhqK6ABLOmKFg4QIvLfh3r6rBxJL7ZRn0T5hbPYjHfzHx7l+aoDo4H\n" +
            "r7daKV6r";

    public static final String TRAVEL_ITINERARY_EMAIL_TEMPLATE_PATH = "/mailTemplate/TravelItineraryTemplate.html";
    public static final String TRAVEL_ITINERARY_SEGMENT_TEMPLATE_PATH = "/mailTemplate/TravelItinerarySegmentTemplate.html";

    public static final String TRAVEL_ITINERARY_EMAIL_ENGLISH_TEMPLATE_PATH = "/mailTemplate/TravelItineraryEnglishTemplate.html";
    public static final String TRAVEL_ITINERARY_ENGLISH_SEGMENT_TEMPLATE = "/mailTemplate/TravelItineraryEnglishSegmentTemplate.html";

    public static final String COMPENSATE_FW_GATEWAY_NO = get("compensate.fw.gatewayNo");
    public static final String COMPENSATE_FW_KEY = get("compensate.fw.key") == null ? "" : get("compensate.fw.key");
    public static final String COMPENSATE_FW_APPID = get("compensate.fw.appId");
    public static final String COMPENSATE_API_REDIRECTURL = get("compensate.api.redirectUrl");

    /**
     * 会员卡样式
     */
    public static final String MEMBER_CARD_COMMON_URL = "https://mediaws.juneyaoair.com/upload/memberCard/common.png";
    public static final String MEMBER_CARD_SILVER_URL = "https://mediaws.juneyaoair.com/upload/memberCard/silver.png";
    public static final String MEMBER_CARD_GOLDEN_URL = "https://mediaws.juneyaoair.com/upload/memberCard/golden.png";
    public static final String MEMBER_CARD_BLACK_URL = "https://mediaws.juneyaoair.com/upload/memberCard/black.png";

    /**
     * 免票查询
     */
    public static final String QUERY_FREE_CALENDAR = "/Shop/QueryFreeCalendar";

    /** 调用新接口的订单类型 */
    public static final String NEW_ORDER_COUPON_SOURCE = get("newOrder.couponSource");

    /**
     * PDI产品查询
     */
    public static final String PDI_URL = get("pdi.url");
    public static final String PDI_CORPID= get("pdi.corpId");
    public static final String PDI_SECRETKEY = get("pdi.secretKey");
    public static final String PDI_GET_TOKEN = "/auth/token";
    public static final String PDI_GET_PRODUCTS = "/productforAL/v2/getProducts";
    public static final String PDI_CREATE_ORDER = "/airlineOrder/creat";
    public static final String PDI_CANCEL_ORDER = "/airlineOrder/cancle";

    /**
     * 阿里云AccessToken
     */
    public static final String ALIYUN_ACCESS_TOKEN_URL = "https://openapi.alipay.com/gateway.do";

    public static final String FLIGHTBASCI_URL = get("flightbasic.url");
    public static final String ALIPAY_QUERY_BIND_RECORD_URL = "/indexHandle/searchAliBindingList";
    public static final String ALIPAY_MEMBER_SAVE_URL = "/indexHandle/saveAliBindingRelationship";
    public static final String FLIGHTBASIC_CHECKSCORERULE = "/risk/checkScoreRule";

    /** 支付宝小程序 */
    public static final String ALIPAYJ_APP_ID = "2021002195694445";
    // 支付宝公钥
    public static final String ALIPAYJJ_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAilq8J9GNve5xdCLhXjnEnl34BIHFnHi3CU5PnsY30KFHr62glk1bF1xxoxEwK0ETO40JXbrSDeSwaV6av8bNR3XlBv321TtvLFWmpDX49HF7wTu9BMYRSdLifseuYkq1RDfFKuRzShpKpGRaymvKGM/TOK3RKnYADmsEcOVQtKwNmuhbBbmcPqW8qtsfrsygDPLDksnU0GXJ1Ci7cDxf/J8LRn1nKpqJq8IOXYn+X7fqBgQrTjFFIbGpBPcylQQUhs7KQFcy1Mw+cTDMgEiIQUwpjoLwJUyt2mV/CzPJ6D9GjutA9AvRn+6QuyAwpmfk8/ePXk0DqxuvkImSdBpPAQIDAQAB";
    // 应用公钥
    public static final String ALIPAYJ_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4C9eAbEH4xtlSBqOHMNnCuBd9MWJXN48BCzqe1J7anpEYKKNhElAxxGsoXDJW1QcuLDKi1p9kW6HlYLcUp1wiOQa3Bv4z/eB4oxqBz6Jx1+ipDD456IlsKh/tOaPkN8koPbGtoct3EXXR76BN/iIewpoPEiOoI/168Uk4Ro5HelulxojF6CyxbQDoLmWcsbFBeXTgHNnM4yOBOybcAJU+oyDPaa+MDRCZ1aUSxxIwm1xuZeRVoV7JDG3UQUs23UrOijKOpXYureeK1aXu/afgUn+Ch0xFzFHJib0LvE12vWKhxPOYMBZALmYwaW9STxPs+P07vEd9frzXepRTZnWawIDAQAB";
    // 应用私钥
    public static final String ALIPAYJ_PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDgL14BsQfjG2VIGo4cw2cK4F30xYlc3jwELOp7UntqekRgoo2ESUDHEayhcMlbVBy4sMqLWn2RboeVgtxSnXCI5BrcG/jP94HijGoHPonHX6KkMPjnoiWwqH+05o+Q3ySg9sa2hy3cRddHvoE3+Ih7Cmg8SI6gj/XrxSThGjkd6W6XGiMXoLLFtAOguZZyxsUF5dOAc2czjI4E7JtwAlT6jIM9pr4wNEJnVpRLHEjCbXG5l5FWhXskMbdRBSzbdSs6KMo6ldi6t54rVpe79p+BSf4KHTEXMUcmJvQu8TXa9YqHE85gwFkAuZjBpb1JPE+z4/Tu8R31+vNd6lFNmdZrAgMBAAECggEBAKvvyCJ1+r2UakY1j57BwoqV75ll0ub1zoPJSwDQDrVoFn8izHSTlenXRVX/4X548Ro5BO6BtWJPqg7h8UYvV3q1r1/22AnwIp504HCz5wmQ8HUK1isYsd1eLau5VIxgh4AoI9kjremNawGsOwCJTWtIXQ82+qRsJMjxR0qZSQsFaFN4MEmWFk9mzPBsMkl2nJu/1v9/m07LGDnz43fEYDNQ7uJLL+EwORdodsXkoYdPqt04W4V1MhjdDiN/N1Bi0lK0aKyEjnZuLYHNSyeb+hAAbgkRIoCtDiUZmFM6Fuoq4fXZ0ByPLhjsxyZXrgsDZ1d1vonqW6ru9NEu8CgthGECgYEA90RUqFC20V0XRyHbkIL47u8JFSS8kAbYWvjaxla9UgmrZ6VF+dHyK2zdBk7p87oq10DYukl1WMXC3wV18FeHzvxPMvbr4eC13ESWapqNMMuU0AdXg8LpCnDMB+Qz2bStA6pWg/4jl7lIjWTei/Zw/mKorDatzURm1F2KYiUE5ZECgYEA6BpXUyxg7LPwtNGoHj5GkLu08k/3WNXkp/YnKm1QPLHEyI85opKleiuphtKAZMVEp7MFViNCu4LeeQdpGr/Xz/trrCGuTHailLg4Wxt/ekBJT3nMs+psDGS4hhuYRoxFUUM/G0LMdGQ1avTNFBW5bv8C7su5gVj9Pbpj49C7DjsCgYAr/CG7FzCOKascYi92k3XIuwi4lCJe1HjkJeR057UhxMqS01mRSQF1LBZdKuh/DJzWUZi7+pk/Q16Y4ZXLPSbRcRx9imMHeTughSrkp7158pz0LHoq3B8sVkhVFwkKLxOf1Usyi6C1OqZnUIwuBZawCcjLaaadHw/TsxgMwV0PoQKBgHUmroL9uGJhJJKv7BJvzoBJLjjMIgXF2GbquLQSfthTxgaRPD8Rk9KJbUTuxP2YEGIw2vWyUIGCubYPBn8I6+4/cgZs7aQMkV1NaqyZwYs8HYSu7qK1JRpQUE7oaMDbFjXBIn4pBjeNSOUWcNi9al5a+uj7DUP7Xb8lcDvR5Eh3AoGAZpjNnqCpOVYQbMCZE3WqCDTecuAOPsogGC8Ru4IMP1KF1y4mZkH/tCp9HQ7TKukZfYKmK34MR8ayOvrN0bLVTgQ3bKr9UPytVQ+50e70AH2tdDmBuCHKjMmYL6L0EJNsHIUN9lQE7kOAD1iSwVGvGeJdX/gTmFlzLdeLLnlgoS8=";
    // 加解密秘钥
    public static final String ALIPAYJ_ENCRYPTE_KEY ="Fs2a3PbyIwXxbeDlRogBKA==";

    /** 支付宝插件 */
    public static final String ALIPAY_PLUG_APP_ID = get("alipay.plug.appId");
    public static final String ALIPAY_PLUG_APP_PRIVATE_KEY = get("alipay.plug.appPrivateKey");
    public static final String ALIPAY_PLUG_APP_PUBLIC_KEY = get("alipay.plug.appPublicKey");

    /** 支付宝插件 */
    public static final String ALIPAY_WINDOW_ID = get("alipay.window.appId");
    public static final String ALIPAY_WINDOW_PRIVATE_KEY = get("alipay.window.privateKey");
    public static final String ALIPAY_WINDOW_PUBLIC_KEY = get("alipay.window.publicKey");

    /** 呼叫中心 open api */
    public static final String URL_CALLCENTER_OPEN_API = get("callcenter.openapi.url");
    public static final String HOCC_PASSENGER_SERVICE_ORDER = "/passengerServiceOrder/save";
    public static final String CREATE_ABNORMAL_FEEDBACK = "/Feedback/CreateAbnormalFeedback";

    /** 微信API */
    public static final String WEIXIN_API_URL = get("weixin.api.url");
    public static final String JSCODE2SESSION = "/sns/jscode2session";
    /** cuss短信签名盐值 */
    public static final String CUSS_SALTY_SMS = get("cuss.salty.sms");
    /** 基础服务平台-支付宝用户获取最近一次行程信息(国内行程) */
    public static final String ALIPAY_QUERY_USER_FLIGHT_TOUR = "/alipayPlug/queryUserFlightTour";
    /** 哈喽租车 */
    public static final String HELLO_BIKE_URL = get("hellobike.url");
    public static final String HELLO_BIKE_HB_PUBLIC_KEY = get("hellobike.hb.publicKey");
    public static final String HELLO_BIKE_HO_PRIVATE_KEY = get("hellobike.ho.privateKey");
    public static final String HELLO_BIKE_HO_PUBLIC_KEY = get("hellobike.ho.publicKey");
    public static final String HO_TOKEN_SM4_KEY = get("hoTokenSm4Key");
    /**
     * <AUTHOR>
     * @Description 学生认证基础服务地址
     * @Date 10:19 2023/6/28
     * @param null
     * @return
     * @return null
     **/public static final String TO_CHECK_STUDENT_QUALIFICATION = "/activity/studentVerify/toCheckStudentQualification";

    /**
     * 查询有效标签
     */
    public static final String TO_QUERY_TAG = "/activity/studentVerify/toQueryTag";

    public static final String TO_QUERY_TAG_V2 = "/activity/studentVerify/toQueryTagV2";

    /**
     * 新增标签
     */
    public static final String TO_ADD_TAG = "/activity/studentVerify/toAddTag";

    /**
     * <AUTHOR>
     * @Description 企业会员认证
     * @Date 10:59 2023/7/18
     **/
    public static final String TO_TAKE_COMPANYMEMBER_APPLY = "/CrmMember/companyMember/photoVerifyApply";

    /**
     * <AUTHOR>
     * @Description 企业会员认证结果查询
     * @Date 12:15 2023/7/18
     **/
    public static final String TO_TAKE_COMPANYMEMBER_RESULT = "/CrmMember/companyMember/queryInfo/v2";


    public static final String PAY_PASSWORD = "/CrmAccount/payPassword";

    /**
     * <AUTHOR>
     * @Description 合作伙伴会员匹配吉祥航空会员活动进度查询
     * @Date 15:16 2024/8/1
     **/
    public static final String TO_CATCH_CHALLENGE_RESULT = "/CrmMember/activity/vipCardActivityQuery";

    /**
     * <AUTHOR>
     * @Description 企业会员解绑
     * @Date 15:24 2023/7/18
     **/
    public static final String TO_TAKE_COMPANYMEMBER_UNBIND = "/CrmMember/companyMember/unbound";

    /** 订单产品服务地址 */
    public static final String ONEORDER_PRODUCT_URL = get("oneorder.product.url");
    /** 接送机-可用城市查询 */
    public static final String AIRTRANS_GET_ALL_CITIES = "/airtrans/GetAllCities";
    /** 接送机-获取预估价 */
    public static final String AIRTRANS_GET_ESTIMATE_PRICE = "/airtrans/GetEstimatePrice";
    /** 接送机-下单 */
    public static final String AIRTRANS_CREATE_ORDER = "/airtrans/CreateOrder";
    /** 接送机-获取接送机订单详情 */
    public static final String AIRTRANS_GET_ORDER_DETAIL = "/airtrans/OrderDetail";
    /** 接送机-获取司机位置 */
    public static final String AIRTRANS_GET_DRIVER_POSITION = "/airtrans/GetDriverPosition";
    /** 接送机-订单取消 */
    public static final String AIRTRANS_CANCEL_ORDER = "/airtrans/CancelOrder";
    /** 接送机-服务取消 */
    public static final String AIRTRANS_CANCEL_SERVICE = "/airtrans/CancelService";
    /** 接送机-查询可用优惠券 */
    public static final String AIRTRANS_QUERY_COUPON = "/airtrans/QueryCoupon";
    /** 接送机-查询预定规则 */
    public static final String AIRTRANS_GET_ORDER_RULE = "/airtrans/GetOrderRule";
    /** 接送机-查询取消规则 */
    public static final String AIRTRANS_GET_CANCEL_RULE = "/airtrans/GetCancelRule";
    /**吉简餐食配置查询**/
    public static final String LOWCARBON_QUERYAVAILFLIGHT = "/lowCarbon/queryAvailFlight";



    /**
     * <AUTHOR>
     * @Description 查询节假日列表信息V2
     * @Date 13:41 2024/3/8
     **/
    public static final String TO_CATCH_CALENDER_INFO = "/cityLowPrice/toCatchHolidayCalender";

    /**
     * 查询航线标签
     */
    public static final String BASIC_ROUTE_LABEL = "/basic/routeLabel/query";

    /**
     * 中转运价 - 中转城市查询 QueryTransferCitys
     */
    public static final String QUERY_SHOP_QUERY_TRANSFER_CITYS = "/Shop/QueryTransferCitys";
}
