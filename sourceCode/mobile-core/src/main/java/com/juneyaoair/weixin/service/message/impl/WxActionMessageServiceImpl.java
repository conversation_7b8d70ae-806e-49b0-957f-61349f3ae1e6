package com.juneyaoair.weixin.service.message.impl;

import com.juneyaoair.weixin.bean.message.ActionMessage;
import com.juneyaoair.weixin.dao.message.IWxActionMessageDao;
import com.juneyaoair.weixin.service.message.IWxActionMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * Created by zc on 2017/1/17.
 */
@Service
public class WxActionMessageServiceImpl implements IWxActionMessageService {
    @Autowired
    private IWxActionMessageDao actionMessageDao;

    @Override
    public List<ActionMessage> queryActionMessageList(ActionMessage actionMessage) {
        return actionMessageDao.queryActionMessageList(actionMessage);
    }

    @Override
    public int queryActionMessageListCnt() {
        return actionMessageDao.queryActionMessageListCnt();
    }

    @Override
    public ActionMessage queryActionMessageById(String id) {
        return actionMessageDao.queryActionMessageById(id);
    }

    @Override
    public void updateActionMessage(ActionMessage actionMessage) {
        actionMessageDao.updateActionMessage(actionMessage);
    }

    @Override
    public void deleteActionMessage(ActionMessage actionMessage) {
        actionMessageDao.deleteActionMessage(actionMessage);
    }

    @Override
    public void addActionMessage(ActionMessage actionMessage) {
        String id = UUID.randomUUID().toString();
        actionMessage.setId(id.replaceAll("-",""));
        actionMessageDao.addActionMessage(actionMessage);
    }
}
