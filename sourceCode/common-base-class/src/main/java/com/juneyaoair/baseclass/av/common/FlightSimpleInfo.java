package com.juneyaoair.baseclass.av.common;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 航班简要信息
 * @date 2024/5/28 10:09
 */
@Data
@NoArgsConstructor
@ApiModel("航班简要")
public class FlightSimpleInfo {
    @ApiModelProperty(value = "航班日期")
    private String flightDate;
    @ApiModelProperty(value = "航班出发时刻 yyyy-MM-dd HH:mm")
    private String depDateTime;
    @ApiModelProperty(value = "航班到达时刻 yyyy-MM-dd HH:mm")
    private String arrDateTime;
    @ApiModelProperty(value = "市场方航班号")
    private String flightNo;
    @ApiModelProperty(value = "承运方航班号")
    private String carryNo;
    @ApiModelProperty(value = "航班跨天数")
    private int days;
}
