package com.juneyaoair.baseclass.av.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2019/7/16  18:52.
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FlightNoIcon {
    private String airComName;
    private String flightNo;
    private String airIcon;
    private String airComText;

    public FlightNoIcon(String flightNo){
        this.flightNo = flightNo;
    }
}
