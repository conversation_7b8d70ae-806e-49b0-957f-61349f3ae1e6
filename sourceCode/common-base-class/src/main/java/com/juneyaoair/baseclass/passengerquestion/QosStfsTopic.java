package com.juneyaoair.baseclass.passengerquestion;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2021-3-23 18:29
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class QosStfsTopic {
    private String createUserId;
    private String createUserName;
    private String createDate;
    private String updateUserId;
    private String updateUserName;
    private String updateDate;
    /**
     * 记录状态:{1:在用,0:弃用}
     */
    private String status;
    private String id;
    /**
     * 题目大类
     */
    private String topicClass;
    /**
     * 题目小类（调研环节）
     */
    private String topicSubclass;
    /**
     * 题型
     */
    private String topicType;
    /**
     * 题目内容
     */
    private String topicContent;
    /**
     * 是否计入总分标记 1 计入总分 0 不计入总分
     */
    private String isCountFlag;
    /**
     * 适用对象范围集合       没有值
     */
    private QosStfsTopicTarget targetList;
    /**
     * 所属部门集合 ,  没有值
     */
    private List<QosStfsTopicDept> deptList;
    /**
     * 所属部门(导出使用)
     */
    private String deptListCh;
    /**
     * 包含的答案集合
     */
    private List<QosStfsAnswer> answerList;
    /**
     * 乘客选中的答案集合   没有值
     */
    private List<QosStfsFeebackQstTop> selectedAnswerList;
    /**
     * 包含的答案(导出使用)
     */
    private String answerListCh;
    /**
     * 答案限选项数
     */
    private Integer answersLimited;
    /**
     * 序号
     */
    private String sortNo;
    /**
     * 题号
     */
    private String topicNo;
    /**
     * 不满意问题以及答案   没有值
     */
    private QosStfsUnsatisQuestion unsatisQuestion;
    /**
     * 是否必选题：1必选，2可选
     */
    private String isRequired;
}
