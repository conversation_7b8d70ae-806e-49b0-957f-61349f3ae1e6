package com.juneyaoair.baseclass.routelabel;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RouteLabelDTO {

    @ApiModelProperty(value = "主键", example = "uuid")
    private String id;


    @ApiModelProperty(value = "标签名称", example = "中转住宿")
    private String labelName;
    /**
     * 标签图片
     */
    @ApiModelProperty(value = "标签图片")
    private String labelImg;
    /**
     * 展示区域标题
     */
    @ApiModelProperty(value = "展示区域标题")
    private String displayAreaTitle;
    /**
     * 展示区域副文字
     */
    @ApiModelProperty(value = "展示区域副文字")
    private String displayViceText;
    /**
     * 展示区域主文
     */
    @ApiModelProperty(value = "展示区域主文")
    private String displayMainText;

    @ApiModelProperty(value = "生效时间", example = "yyyy-MM-dd HH:mm:ss")
    private String startDate;


    @ApiModelProperty(value = "失效时间", example = "yyyy-MM-dd HH:mm:ss")
    private String endDate;


    @ApiModelProperty(value = "标签功能;中转住宿:TransitAccommodation", example = "TransitAccommodation")
    private String labelFunction;


    @ApiModelProperty(value = "标签展示类型", example = "普通标签/活动标签/主要标签,normal/activity/main")
    private String labelDisplayType;


    @ApiModelProperty(value = "启用状态;启用/禁用", example = "true")
    private boolean enableStatus;


    @ApiModelProperty(value = "排序", example = "1")
    private Short sortNum;


    @ApiModelProperty(value = "备注", example = "无")
    private String remark;


    @ApiModelProperty(value = "创建人", example = "admin")
    private String createdBy;


    @ApiModelProperty(value = "创建时间", example = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;


    @ApiModelProperty(value = "更新人", example = "admin")
    private String updatedBy;


    @ApiModelProperty(value = "更新时间", example = "yyyy-MM-dd HH:mm:ss")
    private Date updatedTime;


    @ApiModelProperty(value = "渠道列表")
    private List<RouteLabelChannelDTO> channelList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RouteLabelChannelDTO {

        @ApiModelProperty(value = "主键", example = "uuid")
        private String channelId;


        @ApiModelProperty(value = "渠道", example = "MOBILE")
        private String channel;


        @ApiModelProperty(value = "渠道链接", example = "url")
        private String channelUrl;

    }


    @ApiModelProperty(value = "标签具体规则")
    private List<RouteLabelRuleDTO> ruleDTOList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RouteLabelRuleDTO {

        @ApiModelProperty(value = "主键", example = "uuid")
        private String ruleId;


        @ApiModelProperty(value = "规则名称", example = "中转住宿")
        private String labelRuleName;


        @ApiModelProperty(value = "航线日期-开始", example = "yyyy-MM-dd")
        private String routeStartDate;


        @ApiModelProperty(value = "航线日期-结束", example = "yyyy-MM-dd")
        private String routeEndDate;


        @ApiModelProperty(value = "起飞机场;' / '拼接", example = "BUH/LCA/IST/IZM/CAI/BEY/TIA/SOF")
        private String depAirport;


        @ApiModelProperty(value = "起飞机场航站楼;' / '拼接", example = "T1/T2")
        private String depTerminal;


        @ApiModelProperty(value = "起飞国家;' / '拼接", example = "CN/HK/MO")
        private String depCountry;


        @ApiModelProperty(value = "起飞地区;' / '拼接", example = "EU")
        private String depRegion;


        @ApiModelProperty(value = "到达机场;' / '拼接", example = "BUH/LCA/IST/IZM/CAI/BEY/TIA/SOF")
        private String arrAirport;


        @ApiModelProperty(value = "到达机场航站楼;' / '拼接", example = "T1/T2")
        private String arrTerminal;


        @ApiModelProperty(value = "到达国家;' / '拼接", example = "CN/HK/MO")
        private String arrCountry;


        @ApiModelProperty(value = "到达地区;' / '拼接", example = "EU")
        private String arrRegion;


        @ApiModelProperty(value = "承运航司;两段' - '拼接", example = "HO-AY")
        private String carrier;


        @ApiModelProperty(value = "是否中转;是/否,1/0", example = "1")
        private String transit;


        @ApiModelProperty(value = "中转机场;' / '拼接", example = "BUH/LCA/IST/IZM/CAI/BEY/TIA/SOF")
        private String transAirport;


        @ApiModelProperty(value = "中转出发航站楼;' / '拼接", example = "T2")
        private String transDepTerminal;


        @ApiModelProperty(value = "中转要求同场转机;需同场转机/要求不同机场/无限制，Y/N/A", example = "Y")
        private String transSameAirport;


        @ApiModelProperty(value = "中转时长要求-分钟;x-xxxx", example = "360-2880")
        private String transTime;


        @ApiModelProperty(value = "中转日期限制;隔夜-overnight 当日-sameDay  多个使用英文逗号分割", example = "overnight")
        private String transDateLimit;


        @ApiModelProperty(value = "中转前序航班日期间隔限制;null表示无限制 0-表示当日内 1-表示允许跨一天", example = "0")
        private Short transPreFlightDateLimit;


        @ApiModelProperty(value = "中转后序航班日期间隔限制;null表示无限制 0-表示当日内 1-表示允许跨一天", example = "1")
        private Short transNextFlightDateLimit;


        @ApiModelProperty(value = "启用状态;启用/禁用，1/0", example = "true")
        private boolean enableStatus;


        @ApiModelProperty(value = "排序", example = "1")
        private Integer sortNum;


        @ApiModelProperty(value = "备注", example = "无")
        private String remark;


        @ApiModelProperty(value = "创建人", example = "admin")
        private String createdBy;


        @ApiModelProperty(value = "创建时间", example = "yyyy-MM-dd HH:mm:ss")
        private Date createdTime;


        @ApiModelProperty(value = "更新人", example = "admin")
        private String updatedBy;


        @ApiModelProperty(value = "更新时间", example = "yyyy-MM-dd HH:mm:ss")
        private Date updatedTime;


        @ApiModelProperty(value = "适用航班号;多个以英文逗号分隔，支持中转航班组合", example = "HO1177,HO1127,HO1175")
        private String applyFlightNo;


        @ApiModelProperty(value = "适用机型;多个以英文逗号分隔，仅直达航线生效", example = "320,321,789,32N,32S,32Q,32A")
        private String applyAircraftType;
    }
}
