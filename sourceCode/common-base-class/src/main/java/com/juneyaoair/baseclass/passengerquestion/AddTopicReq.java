package com.juneyaoair.baseclass.passengerquestion;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * @Classname AddTopicReq
 * @Description 提交问卷请求
 * @Date 2019/10/29 17:42
 * @Created by yzh
 */
@Data
public class AddTopicReq extends UserInfoMust {
    @NotEmpty(message = "题目答案不能为空")
    private List<CommitTopicData> topicList;
    @NotBlank(message = "问卷ID不能为空")
    private String feebackId;
    @NotBlank(message = "题目类别不能为空" )
    @Pattern(regexp="[0123456789]{1}", message = "题目类别错误")
    private String topicClass;
}
