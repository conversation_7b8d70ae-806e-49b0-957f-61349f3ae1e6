package com.juneyaoair.baseclass.individualizationSetMeal.response.idvmMealQueryResp;

import lombok.Data;

import java.util.List;

/**
 * @Classname IdvmMealQueryApiResp
 * @Description 返回给app的数据
 * @Date 2019/8/2 9:11
 * @Created by yzh
 */
@Data
public class IdvmMealQueryApiResp {
    //航线类型 1：中，短航线-可选一餐 2：中长，长航线-可选两餐 0：无可选餐食
    private String flightCategory;
    //餐食种类 TAB选项
    private List<MealTab> mealTab;
    //舱位类型
    private String cabinType;
}
