package com.juneyaoair.baseclass.intlRescheduleRefund;


import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IntlRescheduleRefundRequest {

    @ApiModelProperty(value = "旅客姓名")
    String passName;

    @ApiModelProperty(value = "证件号码（护照号码）")
    String certNo;

    @ApiModelProperty(value = "票号")
    String ticketNo;

    @ApiModelProperty(value = "联系人姓名")
    String contactName;

    @ApiModelProperty(value = "联系人号码")
    String contactTel;

    @ApiModelProperty(value = "联系人邮箱")
    String contactEmail;

    @ApiModelProperty(value = "服务类型（IRCS）")
    String serviceType;

    @ApiModelProperty(value = "选择人工客服联系时间(上午A.M/下午P.M /晚上Evening)")
    String serviceTime;

    @ApiModelProperty(value = "旅客诉求(改期Change/退票Refund)")
    String passAppeal;

    @ApiModelProperty(value = "备注（用来填写其他）")
    String remark;

    @JsonIgnore
    String userNo;

    @JsonIgnore
    String ip;

}
