package com.juneyaoair.baseclass.silverFollowGold.thirdPart;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName SliverCardApplyReqDto
 * @Description
 * <AUTHOR>
 * @Date 2024/2/26 13:30
 * @Version 1.0
 */

@Data
@Builder
public class SliverCardApplyReqDto {

    /**
     * <AUTHOR>
     * @Description 被邀请人(证件号码)
     * @Date 13:30 2024/2/26
     **/
    @JsonProperty("CertificateNumber")
    private String CertificateNumber;

    /**
     * <AUTHOR>
     * @Description 邀请人会员id
     * @Date 13:30 2024/2/26
     **/
    @JsonProperty("MemberId")
    private String MemberId;


    /**
     * <AUTHOR>
     * @Description 被邀请人(手机号)
     * @Date 13:31 2024/2/26
     **/
    @JsonProperty("Mobile")
    private String Mobile;


    /**
     * <AUTHOR>
     * @Description 被邀请人(姓名)
     * @Date 13:31 2024/2/26
     **/
    @JsonProperty("Name")
    private String Name;


}
