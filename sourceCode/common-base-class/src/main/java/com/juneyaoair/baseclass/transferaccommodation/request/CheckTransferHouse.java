package com.juneyaoair.baseclass.transferaccommodation.request;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * @ClassName CheckTransferHouse
 * @Description
 * <AUTHOR>
 * @Date 2019/10/29 16:17
 **/
@Data
public class CheckTransferHouse {
    @SerializedName("CertNo")
    private String certNo;  //证件号
    @SerializedName("TktNo")
    private String tktNo;  //客票号
    @SerializedName("CouponSource")
    private String couponSource;  // 权益订单类型
    @SerializedName("FlightDate")
    private String flightDate;  //航班日期 yyyy-MM-dd
    @SerializedName("FlightNo")
    private String flightNo;  // 航班号
    @SerializedName("ApplyResult")
    private Boolean applyResult;  // 是否已申请
    @SerializedName("OrderNo")
    private String orderNo;  //订单编号
    @SerializedName("Contact")
    private String contact;  // 联系人名称
    @SerializedName("CreateDateTime")
    private Long createDateTime;  //下单时间
    @SerializedName("PhoneNo")
    private String phoneNo;  //联系电话
}
