package com.juneyaoair.baseclass.electroniccompensation.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 请求后端信息
 *
 * <AUTHOR>
 * @project mobile
 * @create 2018-11-19 17:49
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class ECPersonInfo {
    private String page; // 当前页
    private String pageSize; // 当前每页数量
    private String pass_Idcard_Type;
    private String pass_Idcard;
    private String flight_date;
    private String ticket_no;
    private String flight_no;
    private String passName;
    private String id;

    //2021-7-7 方案id
    private String comp_scheme_id;
    //国际国内标识 D-国内 I-国际
    private String compScene;
}
