package com.juneyaoair.baseclass.electroniccompensation.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-09-25 9:01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ECSimplePassenger {

    /**
     * 姓名
     */
    private String n;

    /**
     * 补偿金额
     */
    private BigDecimal a;

    /**
     * 关系
     */
    private String r;

}
