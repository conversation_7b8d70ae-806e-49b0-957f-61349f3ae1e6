package com.juneyaoair.baseclass.individualizationSetMeal.request;

import com.juneyaoair.baseclass.flightchange.request.GeetestInitialization;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

@Data
public class IndividualizationSetMealReq extends GeetestInitialization {
    private String flightNo;
    private String flightDate;
    private String depCityCode;
    private String arrCityCode;
    private String cabinType;
    private String passName;
    private String ffpCardNo;
    @NotBlank(message = "用户编号不能为空")
    private String ffpId;
    private String loginKeyInfo;
    private String departureStation;
    private String arrivalStation;
    private String passIdcard;
    private String accountContact;
    private String idvmMealName;
    private String idvmMealImage;
    private String idvmMealCode;
    private String id;
    private String dealStatus;
    private String ticketNo;
    private String version;
    private String idNo;
    private String mealCategory;
}
