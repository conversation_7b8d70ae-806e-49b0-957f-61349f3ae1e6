package com.juneyaoair.baseclass.electroniccompensation.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date ：Created in 2020-12-14 14:55
 * @description：
 * @modified By：
 * @version: $
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ECPersonCashReq {

    private String passIdCard;
    private String ticketNo;
    private String flightNo;
    private String flightDate;
}
