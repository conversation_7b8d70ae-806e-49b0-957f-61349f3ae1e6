package com.juneyaoair.baseclass.transferaccommodation.resonse;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.baseclass.transferaccommodation.request.FlightInformation;
import lombok.Data;

import java.util.List;

/**
 * @ClassName GetOrderDetailResponseDto
 * @Description 中转住宿订单详情
 * <AUTHOR>
 * @Date 2019/10/31 10:56
 **/
@Data
public class GetOrderDetailResponseDto {

    @SerializedName("OrderNo")
    private String orderNo;  //订单编号
    @SerializedName("CertNo")
    private String certNo;  // 证件号
    @SerializedName("TktNo")
    private String tktNo;  // 客票号
    @SerializedName("CertType")
    private String certType;  // 证件类型
    @SerializedName("PassengerName")
    private String passengerName;  // 乘客姓名
    @SerializedName("PhoneNo")
    private String phoneNo;  // 联系人手机号
    @SerializedName("Contact")
    private String contact;  // 联系人
    @SerializedName("CreateDateTime")
    private Long createDateTime;  // 订单创建时间
    @SerializedName("FlightInformations")
    private List<FlightInformation> flightInformations; // 航程信息
}
