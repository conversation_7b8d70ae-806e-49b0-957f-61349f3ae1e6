package com.juneyaoair.baseclass.passengerquestion;

import lombok.Data;

import java.util.List;

/**
 * @Classname TopicDetail
 * @Description 题目详情
 * @Date 2019/10/28 10:33
 * @Created by yzh
 */
@Data
public class TopicDetail {
    private String id; //题目主键吗
    private String topicClass; //题目大类
    private String topicSubclass; //题目小类
    /**
     * 题型
     * 1-单选题 2-多选题
     */
    private String topicType; //
    private String topicContent; //题目内容
    private String isCountFlag; //是否计入总分标记
    private Integer sortNo; //题目序号
    private String topicNo; //题号
    /**
     * 多选题  可选答案上限
     */
    private Integer answersLimited; //多选题限制选项个数
    private List<AnswerDetail> answerList; //答案集合
    private UnsatisQuestionDetail unsatisQuestion; //不满意问题及答案
    private String fatherId;
}
