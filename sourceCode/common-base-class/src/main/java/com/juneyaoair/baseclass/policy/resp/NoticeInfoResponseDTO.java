package com.juneyaoair.baseclass.policy.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName NoticeInfoResponseDTO
 * @Description
 * <AUTHOR>
 * @Date 2019/9/4 10:06
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoticeInfoResponseDTO {

    /**
     * 字典值名称
     */
    private String dictValueName;

    /**
     * 结果集
     */
    private List<NoticeResponseDTO> noticeResponseDTOs;

}
