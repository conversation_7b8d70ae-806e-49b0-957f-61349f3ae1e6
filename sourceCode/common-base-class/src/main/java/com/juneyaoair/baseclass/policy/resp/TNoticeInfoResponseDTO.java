package com.juneyaoair.baseclass.policy.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Classname TNoticeInfoResponseDTO
 * @Description 条款文本查询结果
 * @Date 2019/12/27 10:00
 * @Created by yzh
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TNoticeInfoResponseDTO {
    private String ntInfoId; //条款id
    private String noticeId; //父模块id
    private String ntInfoName; //条款名称
    private String ntInfoUrl; //条款地址
    private String createTime; //创建时间
    private String modifyTime; //修改时间
    private String person; //修改人
    private String ntInfoCode; //条款序号
    private String ntInfoDescription; //条款描述
    private String canInfoShow; //是否展示
    private byte[] richText; //富文本内容
}
