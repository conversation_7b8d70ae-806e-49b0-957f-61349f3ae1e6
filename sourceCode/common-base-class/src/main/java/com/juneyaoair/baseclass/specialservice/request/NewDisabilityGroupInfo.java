package com.juneyaoair.baseclass.specialservice.request;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.Max;

/**
 * Created by guan<PERSON>yin on 2018/11/11.
 * 残疾人团队请求信息
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class NewDisabilityGroupInfo extends UserInfoMust {
    /**
     * 残疾人团队人数
     */
    @Valid
    @Range(min=0, max=128,message = "残疾人团队人数有误")
    private int disabilityNum;
    /**
     * 残疾类型
     */
    @Valid
    private String disabilityType;
    /**
     * 是否携带轮椅
     */
    @Valid
    private String isCarryWheelchair;
    /**
     * 航班信息
     */
    @Valid
    private NewFlightInfo flightInfo;
    /**
     * 预定人信息
     */
    @Valid
    private NewYdPersonInfo ydPersonInfo;
    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 五类残疾种类
     */
    private String disability;

}
