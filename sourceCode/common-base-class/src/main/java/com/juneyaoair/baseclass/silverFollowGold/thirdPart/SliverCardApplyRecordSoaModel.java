package com.juneyaoair.baseclass.silverFollowGold.thirdPart;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName SliverCardApplyRecordSoaModel
 * @Description
 * <AUTHOR>
 * @Date 2024/2/26 10:35
 * @Version 1.0
 */

@Data
public class SliverCardApplyRecordSoaModel {

    @ApiModelProperty(value = "申请时间")
    @SerializedName("ApplyTime")
    private String applyTime;

    @ApiModelProperty(value = "审核状态(请选择:0 待审核:1 审核拒绝:2 审核通过:3)")
    @SerializedName("AuditStatus")
    private Integer auditStatus;

    @ApiModelProperty(value = "审核状态(请选择:0 待审核:1 审核拒绝:2 审核通过:3)")
    @SerializedName("AuditStatusDesc")
    private String auditStatusDesc;

    @ApiModelProperty(value = "被邀请人(证件号码)")
    @SerializedName("CertificateNumber")
    private String certificateNumber;

    @ApiModelProperty(value = "被邀请人(手机号)")
    @SerializedName("Mobile")
    private String mobile;

    @ApiModelProperty(value = "被邀请人(姓名)")
    @SerializedName("Name")
    private String name;
}
