package com.juneyaoair.baseclass.silverFollowGold.request;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * @ClassName SilverFollowGoldRequest
 * @Description 升金带银请求体
 * <AUTHOR>
 * @Date 2024/2/26 9:52
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SilverFollowGoldRequest extends UserInfoMust {

    @ApiModelProperty(value = "被邀请人姓名")
    @NotEmpty(message = "被邀请人姓名不能为空")
    private String name;

    @ApiModelProperty(value = "被邀请人证件号")
    @NotEmpty(message = "被邀请人证件号不能为空")
    private String certificateNumber;

    @ApiModelProperty(value = "被邀请人手机号")
    @NotBlank(message = "被邀请人手机号不能为空")
    private String mobile;
}
