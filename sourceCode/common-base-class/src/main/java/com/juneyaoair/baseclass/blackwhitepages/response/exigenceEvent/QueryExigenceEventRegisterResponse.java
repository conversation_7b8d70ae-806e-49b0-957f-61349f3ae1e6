package com.juneyaoair.baseclass.blackwhitepages.response.exigenceEvent;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@AllArgsConstructor
@Data
@NoArgsConstructor
public class QueryExigenceEventRegisterResponse {
    private String Version;
    private String ChannelCode;
    private String ResultCode;
    private String ErrorInfo;
    private List<ExigenceRegisterModel> ExigenceEventRegisterLsit;
}
