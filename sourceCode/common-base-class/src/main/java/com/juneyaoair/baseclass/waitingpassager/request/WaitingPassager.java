package com.juneyaoair.baseclass.waitingpassager.request;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import com.juneyaoair.pattern.PatternCommon;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 前端申请候补
 *
 * <AUTHOR>
 * @project mobile
 * @create 2018-11-20 18:42
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class WaitingPassager extends UserInfoMust{
    /**
     * 航班日期
     */
    @NotBlank(message="航班日期")
    private String flightDate;
//    @NotBlank(message="航班ID")
    private String flightId;
    @NotBlank(message="航班号")
    private String flightNo;
    /**
     * 非自愿改签标识[1:是,0:否]
     */
    private String involuntary;
    @NotBlank(message="会员等级")
    private String memberLevel;
    @NotBlank(message="联系人手机号")
    @Pattern(regexp = PatternCommon.MOBILE_PHONE,message = "请输入正确的手机号")
    private String linkPersonContact;
    private String passengerWorth;
    private String registerTime;
    /**
     * 记录状态:{1:在用,0:弃用}
     */
    private String status;
    private String ticketNo;
    /**
     * 候补开放办理时间
     */
    private String waitStartTime;
    @NotBlank(message="起飞城市三字码")
    private String departureAirport;
    @NotBlank(message="落地城市三字码")
    private String arrivalAirport;
    /**
     *  舱位
     */
    private String cabin;
    private List<PassagerInfo> passagerList;
    @NotBlank(message = "联系人姓名")
    private String linkPersonName;
//    @NotBlank(message = "起飞航站楼")
    private String depAirportTerminal;
//    @NotBlank(message = "到达航站楼")
    private String arrAirportTerminal;
    @NotBlank(message = "航班起飞时间")
    private String depTime;
    @NotBlank(message = "航班到达时间")
    private String arrTime;



}
