package com.juneyaoair.baseclass.request.coupons;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 券码兑换请求参数
 * @created 2023/9/8 9:46
 */
@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ExchangeCouponParam extends UserInfoMust {

    @ApiModelProperty(value = "兑换码")
    @NotBlank(message = "兑换码不能为空")
    private String approvalCode;

    @ApiModelProperty(value = "短信验证码")
    @NotBlank(message = "短信验证码不能为空")
    private String verifyCode;

}
