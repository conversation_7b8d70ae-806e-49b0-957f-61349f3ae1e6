package com.juneyaoair.baseclass.electroniccompensation.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date ：Created in 2020-11-27 11:06
 * @description：
 * @modified By：
 * @version: $
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ECompensationIdResult {
    //创建人系统ID
    private String createUserId;
    //创建人名称
    private String createUserName;
    //创建时间
    private String createDate;
    //更新人系统ID
    private String updateUserId;
    //更新人名称
    private String updateUserName;
    //更新时间
    private String updateDate;
    //记录状态:{1:在用,0:弃用}
    private String status;
    //主键
    private String id;
    //旅客姓名
    private String passName;
    // 票号
    private String ticketNo;
    //仓位
    private String cabin;
    //会员等级
    private String memberGrade;
    //会员卡号
    private String memberNo;
    //会员id
    private String memberId;
    //补偿原因
    private String compensationReason;
    //补偿金额
    private double compensationAmount;
    //旅客手机号码
    private String passContact;
    //支付方式
    private String payWay;
    //证件类型
    private String passIdType;
    //证件号
    private String passIdCard;
    //补偿状态{0:申请,1:金额审批,2:旅客信息补充,3:资料审核,4:待补偿,5:已补偿,6:补偿失败} ,
    private String compensationStatus;
    //审批人id
    private String approlUserId;
    //审批人
    private String approlUserName;
    //审批意见
    private String approlInfo;
    //审批时间
    private String approlTime;
    //审核人姓名
    private String checkUserName;
    //审核人id(对旅客提交的资料进行审核)
    private String checkUserId;
    //审核时间
    private String checkTime;
    //处理部门(起单员工的部门id)
    private String createUserDept;
    //收款人姓名
    private String receiptName;
    //收款人证件类型
    private String receiptIdcardType;
    //收款方式【参考补偿方式】
    private String receiptWay;
    //收款账号
    private String receiptAccount;
    //收款金额
    private String receiptAmount;
    //电话号码
    private String phoneNum;
    //收款人账号所属银行名称
    private String receiptAccountBank;
    //收款人与旅客关系
    private String relationship;
    //相关证明附件地址(旅客提交申请的传的附件,存在B2C的附件库中)
    private String relevantProofPath;
    //航班号
    private String flightNo;
    //航班日期
    private String flightDate;
    //出发站
    private String departureStation;
    //达到站
    private String arrivalStation;
    //是否代领{1:是,0否}
    private String takeReceiveFlag;
    //审核意见
    private String checkInfo;
    //认证code
    private String authCode;
    //微信支付openId
    private String openId;
    private String ffpid;
    //赔付时间
    private String payOutTime;
    private String checkBillStatus;
    private String checkBillTaskId;
    //商户订单号
    private String payoutNo;
    //出发时间
    private String depTime;
    //到达时间
    private String arrTime;
    //飞行市场（分钟）
    private String flyTime;
    private String departureStationName;
    //到达站中文名
    private String arrivalStationName;
    //提交时间
    private String passCommitTime;

    //赔付场站
    private String payStation;
    //是否需提交审核材料
    private Boolean dataApproval;
    //凭证附件地址
    private String credentialPath;
}
