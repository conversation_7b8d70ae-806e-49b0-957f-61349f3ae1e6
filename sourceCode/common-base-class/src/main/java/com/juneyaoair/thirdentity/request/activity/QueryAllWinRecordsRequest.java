package com.juneyaoair.thirdentity.request.activity;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "QueryAllWinRecordsRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryAllWinRecordsRequest {
	private String Version;
	private String ChannelCode;
	private String UserNo;
	private Integer Count;
	private Integer DrawId;

	public Integer getCount() {
		return Count;
	}

	public void setCount(Integer count) {
		Count = count;
	}

	public Integer getDrawId() {
		return DrawId;
	}

	public void setDrawId(Integer drawId) {
		DrawId = drawId;
	}

	public QueryAllWinRecordsRequest() {
		super();
	}

	public QueryAllWinRecordsRequest(String version, String channelCode) {
		super();
		Version = version;
		ChannelCode = channelCode;
	}
	
	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}

	public String getUserNo() {
		return UserNo;
	}

	public void setUserNo(String userNo) {
		UserNo = userNo;
	}

}
