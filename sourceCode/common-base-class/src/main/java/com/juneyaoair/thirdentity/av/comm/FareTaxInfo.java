package com.juneyaoair.thirdentity.av.comm;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.juneyaoair.baseclass.response.order.comm.TaxInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2018/12/5  19:53.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FareTaxInfo {
    private String PassengerType; //乘客类型
    private Double CNTax;  //机建
    private Double YQTax;  //燃油
    private Double QTax;   //Q税
    private Double OtherTax; //其他税费
    private List<TaxInfo> TaxInfoList;//税费明细


    public FareTaxInfo(){}
    public FareTaxInfo(String passengerType,Double cnTax,Double yqTax,Double qTax,Double otherTax){
        this.PassengerType = passengerType;
        this.CNTax = cnTax;
        this.YQTax = yqTax;
        this.QTax = qTax;
        this.OtherTax = otherTax;
    }
}
