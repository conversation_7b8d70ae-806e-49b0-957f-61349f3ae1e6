package com.juneyaoair.thirdentity.request.order.refund.wifi;

import com.juneyaoair.baseclass.response.order.detail.WifiBuy;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;


@XmlRootElement(name = "WifiRefundReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtWifiRefundReq {
	private String Version; //接口版本号10
	private String ChannelCode; //渠道用户号B2C,CC等
	private String UserNo; //渠道用户人员号分配给渠道用户的工作人员号

	private String ChannelOrderNo;
	private String OrderNo;
	private String FfpId;
	private String FfpCardNo;
	private List<WifiBuy> WifiBuyList;

	public PtWifiRefundReq() {
		super();
	}

	public PtWifiRefundReq(String version, String channelCode, String userNo) {
		this.Version = version;
		this.ChannelCode = channelCode;
		this.UserNo = userNo;
	}

	public String getVersion() {
		return Version;
	}

	public void setVersion(String version) {
		Version = version;
	}

	public String getChannelCode() {
		return ChannelCode;
	}

	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}

	public String getUserNo() {
		return UserNo;
	}

	public void setUserNo(String userNo) {
		UserNo = userNo;
	}

	public String getChannelOrderNo() {
		return ChannelOrderNo;
	}

	public void setChannelOrderNo(String channelOrderNo) {
		ChannelOrderNo = channelOrderNo;
	}

	public String getOrderNo() {
		return OrderNo;
	}

	public void setOrderNo(String orderNo) {
		OrderNo = orderNo;
	}

	public String getFfpId() {
		return FfpId;
	}

	public void setFfpId(String ffpId) {
		FfpId = ffpId;
	}

	public String getFfpCardNo() {
		return FfpCardNo;
	}

	public void setFfpCardNo(String ffpCardNo) {
		FfpCardNo = ffpCardNo;
	}

	public List<WifiBuy> getWifiBuyList() {
		return WifiBuyList;
	}

	public void setWifiBuyList(List<WifiBuy> wifiBuyList) {
		WifiBuyList = wifiBuyList;
	}
}