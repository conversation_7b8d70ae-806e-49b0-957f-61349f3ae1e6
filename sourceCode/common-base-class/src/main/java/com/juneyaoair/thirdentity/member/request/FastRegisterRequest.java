package com.juneyaoair.thirdentity.member.request;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-07-21 15:33
 */

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class FastRegisterRequest {

    /**
     * 注册方式（1-通过手机号注册 2-通过邮箱注册）
     */
    @SerializedName("Mode")
    private int mode;

    /**
     * 方式值（手机号或邮箱地址）（支持境外手机号，格式：国际区号-手机号 如：852-15066668888）
     */
    @SerializedName("ModeValue")
    private String modeValue;

    /**
     * 验证码
     */
    @SerializedName("Captcha")
    private String captcha;

    /**
     * 密码
     */
    @SerializedName("Password")
    private String password;

    /**
     * 是否忽略验证（true：快速注册无需验证码；false：快速注册需要验证码）
     */
    @SerializedName("IgnoreVerify")
    private boolean ignoreVerify;
}
