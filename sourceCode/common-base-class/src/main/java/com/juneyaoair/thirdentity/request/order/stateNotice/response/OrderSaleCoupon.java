package com.juneyaoair.thirdentity.request.order.stateNotice.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021-3-25 17:29
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class OrderSaleCoupon {
    private String ActivityName;
    private String CouponState;
    private String CouponStateName;
    private String CouponSource;
    private Integer Price;
    private Integer FfpUseScore;
    private Integer CouponPrice;
    private String DepAirport;
    private String VoucherState;
    private String ContactTelphone;
    private String TktNo;
    private String CertNo;
    private String FlightNo;
    private String CouponDynamicParam;
}
