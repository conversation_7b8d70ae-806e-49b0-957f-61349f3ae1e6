package com.juneyaoair.thirdentity.request.checkin;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 单人值机取消
 * <AUTHOR>
 *
 */
@XmlRootElement(name = "HOPsgPwInfoBean")
@XmlAccessorType(XmlAccessType.FIELD)
public class HOPsgPwInfoBean {
	private String fromCity;
	private String certId;
	private String certType;
	private String cellPhone;  //手机号
	private String checkInEmail;//取消值机email
	private String checkInIP;  //前端旅客取消值机所在IP
	private String channelCode;    //值机渠道(网站、手机、微信)
	private String checkCode;
	
	public String getFromCity() {
		return fromCity;
	}
	public void setFromCity(String fromCity) {
		this.fromCity = fromCity;
	}
	public String getCertId() {
		return certId;
	}
	public void setCertId(String certId) {
		this.certId = certId;
	}
	public String getCertType() {
		return certType;
	}
	public void setCertType(String certType) {
		this.certType = certType;
	}
	public String getCellPhone() {
		return cellPhone;
	}
	public void setCellPhone(String cellPhone) {
		this.cellPhone = cellPhone;
	}
	public String getCheckInEmail() {
		return checkInEmail;
	}
	public void setCheckInEmail(String checkInEmail) {
		this.checkInEmail = checkInEmail;
	}
	public String getCheckInIP() {
		return checkInIP;
	}
	public void setCheckInIP(String checkInIP) {
		this.checkInIP = checkInIP;
	}
	public String getChannelCode() {
		return channelCode;
	}
	public void setChannelCode(String channelCode) {
		this.channelCode = channelCode;
	}
	public String getCheckCode() {
		return checkCode;
	}
	public void setCheckCode(String checkCode) {
		this.checkCode = checkCode;
	}
	

	
}
