package com.juneyaoair.thirdentity.request.score;

import com.juneyaoair.thirdentity.request.booking.PtTicketOrderInfo;

/**
 * Created by qinxiaoming on 2016-5-6.
 */
public class ScoreUseRuleRequest {
    private String Version; // 接口版本号10
    private String ChannelCode; // 渠道用户号B2C,CC等
    private String UserNo; // 渠道工作人员号分配给渠道用户的工作人员号

    private PtTicketOrderInfo BookingInfo;



    public ScoreUseRuleRequest() {
    }

    public ScoreUseRuleRequest(String version, String channelCode, String userNo, PtTicketOrderInfo bookingInfo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
        BookingInfo = bookingInfo;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public PtTicketOrderInfo getBookingInfo() {
        return BookingInfo;
    }

    public void setBookingInfo(PtTicketOrderInfo bookingInfo) {
        BookingInfo = bookingInfo;
    }


}
