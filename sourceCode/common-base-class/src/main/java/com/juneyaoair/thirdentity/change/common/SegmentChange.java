package com.juneyaoair.thirdentity.change.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2018/12/21  16:28.
 */
@Data
@NoArgsConstructor
public class SegmentChange {
    private String FlightDirection;
    private String FlightDate; //yyyy-MM-dd
    private String DepCity;
    private String ArrCity;
    private String DepAirport;
    private String ArrAirport;
    private String FlightNo;
    private String Carrier;
    private String DepTime;  //出发时间 HH:mm
    private String Cabins;  //原舱位多个仓位,隔开
    private String Ftype;  //机型
    private List<CabinChange> CabinChangeList;
    private String InssueDate; //出票时间 2021-08-04
    /**
     * 目标航班日期
     */
    private String ChangeFlightDate;


    public SegmentChange(String depCity,String arrCity,String changeFlightDate){
        this.DepCity = depCity;
        this.ArrCity = arrCity;
        this.ChangeFlightDate = changeFlightDate;
    }
}
