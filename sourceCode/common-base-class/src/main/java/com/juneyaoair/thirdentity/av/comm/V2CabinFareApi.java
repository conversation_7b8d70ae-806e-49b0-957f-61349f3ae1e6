package com.juneyaoair.thirdentity.av.comm;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2019/7/10  18:49.
 */
@Data
public class V2CabinFareApi {
    @SerializedName("ID")
    private String id;
    @SerializedName("PassengerType")
    private String passengerType;
    /**
     * STU-留学生运价
     */
    @SerializedName("PassengerIdentity")
    private String passengerIdentity;
    @SerializedName("CabinComb")
    private String cabinComb;
    @SerializedName("ReturnScore")
    private String returnScore;
    @SerializedName("FareKey")
    private String fareKey;
    @SerializedName("CabinNumber")
    private String cabinNumber;
    @SerializedName("CabinClass")
    private String cabinClass;
    //品牌运价名称 品牌模式下包含字段 2021-05-10
    private String BrandName;
    //品牌运价代码 品牌模式下包含字段 2021-05-10
    private String BrandCode;
    //运价产品类型 2021-08-05
    private String PriceProductSign;

}
