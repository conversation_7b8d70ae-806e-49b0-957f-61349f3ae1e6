package com.juneyaoair.thirdentity.member.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Classname MemberEntityCardApplyQueryResponse
 * @Description 会员实体卡申请记录查询
 * @Date 2019/10/21 10:03
 * @Created by yzh
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberEntityCardApplyQueryResponse {
    //申请记录集合
    private List<EntityCardApplyRecordSoaModel> ApplyRecords;
}
