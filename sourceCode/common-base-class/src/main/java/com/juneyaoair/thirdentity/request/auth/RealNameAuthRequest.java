package com.juneyaoair.thirdentity.request.auth;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by yaocf on 2017/1/10.
 */
@XmlRootElement(name = "RealNameAuthRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class RealNameAuthRequest {
    private String Version;//版本号
    private String ChannelNo;
    private String GatewayNo;//网关
    private String ReUserName;//持卡人姓名
    private String DynamicParameters;//动态参数
    private String ChkValue;//签名验证字符串

    public RealNameAuthRequest(String channelNo, String version) {
        ChannelNo = channelNo;
        Version = version;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelNo() {
        return ChannelNo;
    }

    public void setChannelNo(String channelNo) {
        ChannelNo = channelNo;
    }

    public String getGatewayNo() {
        return GatewayNo;
    }

    public void setGatewayNo(String gatewayNo) {
        GatewayNo = gatewayNo;
    }

    public String getReUserName() {
        return ReUserName;
    }

    public void setReUserName(String reUserName) {
        ReUserName = reUserName;
    }

    public String getDynamicParameters() {
        return DynamicParameters;
    }

    public void setDynamicParameters(String dynamicParameters) {
        DynamicParameters = dynamicParameters;
    }

    public String getChkValue() {
        return ChkValue;
    }

    public void setChkValue(String chkValue) {
        ChkValue = chkValue;
    }

    //签名验证字符串
    public String getChkValueStr(){
        String chk=null;
        chk=Version+ChannelNo+GatewayNo+ReUserName+DynamicParameters;
        return chk;
    }
    //获取动态字符串
    public Map<String,String> getParaMap(){
        Map<String,String> param=new HashMap<>();
        param.put("Version",this.Version);
        param.put("ChannelNo",this.ChannelNo);
        param.put("GatewayNo",this.GatewayNo);
        param.put("ReUserName",this.ReUserName);
        param.put("DynamicParameters",this.DynamicParameters);
        param.put("ChkValue",this.ChkValue);
        return param;
    }
}
