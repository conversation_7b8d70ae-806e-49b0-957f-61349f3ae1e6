package com.juneyaoair.thirdentity.ticket.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/25 16:13
 */
@ApiModel(value = "PtTicketListInfoResponse",description = "证件查询客票列表响应")
@Data
public class PtTicketListInfoResponse {
    @ApiModelProperty(value = "渠道用户号")
    private String ChannelCode;
    @ApiModelProperty(value = "客票信息列表")
    private List<PtDetrDigestInfoDto> DetrDigestInfoDtoList;
    @ApiModelProperty(value = "结果代码")
    private String ResultCode;
    @ApiModelProperty(value = "错误信息")
    private String ErrorInfo;
}
