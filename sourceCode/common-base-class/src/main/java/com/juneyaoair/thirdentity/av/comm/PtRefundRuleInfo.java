package com.juneyaoair.thirdentity.av.comm;

import lombok.Data;

/**
 * <AUTHOR>
 * @description  退票规则描述
 * @date 2018/10/30  15:47.
 */
@Data
public class PtRefundRuleInfo {
    /**
     * 客票使用情况
     * 空 - 不使用该条件，0-全部未使用,1-部分已使用
     */
    private String TicketUsage;
    /**
     *  航班起飞时间限制条件
     *  空 - 不使用该条件，0-起飞前,1-起飞后
     */
    private String FlightTimeCondition;
    /**
     *  限制条件开始(值/单位)
     *  格式：值/单位 如：两小时表示为2/H
     *  值：正整数
     *  单位：Y-年，M-月，D-日，H-小时，MI-分钟
     *  FlightTimeCondition为空时不使用
     *  FlightTimeCondition为0时 开始区间时间 = (申请时间 - 航班飞时时间) * -1 >= TimeConditionStart
     *  FlightTimeCondition为1时 开始区间时间 = (申请时间 - 航班飞时时间） >= TimeConditionStart
     */
    private String TimeConditionStart;
    /**
     * 限制条件开始(值/单位)
     * 格式：值/单位 如：两小时表示为2/H
     * 值：正整数
     * 单位：Y-年，M-月，D-日，H-小时，MI-分钟
     * FlightTimeCondition为空时不使用
     * TimeConditionEnd为空时表示开区间
     * FlightTimeCondition为0时 结束区间时间 = (申请时间 - 航班飞时时间) * -1 <= TimeConditionEnd
     * FlightTimeCondition为1时 结束区间时间 = (申请时间 - 航班飞时时间) <= TimeConditionEnd
     */
    private String TimeConditionEnd;
    /**
     * 未使用退票费金额  7(其中2位小数)
     */
    private Double Fee;
    /**
     * 显示用费率  XX%
     */
    private String ShowFeeRate;
    /**
     * 已使用票价金额  7(其中2位小数)
     * 正数 该段票已使用时在计算应退金额时减去已使用段票价时需要采用该字段金额。（打包价部分使用时，可以通过该方法收取惩罚费）
     */
    private Double UsedTicketPrice;
}
