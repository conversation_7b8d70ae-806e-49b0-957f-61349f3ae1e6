package com.juneyaoair.thirdentity.member.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MemberLevelChangeSoaModel
 * @Description 会员级别升降级信息
 * <AUTHOR>
 * @Date 2020/3/6 17:14
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MemberLevelChangeSoaModel {

    /**
     * 升级信息
     */
    private MemberLevelSoaModel UpgradeInfo;
    /**
     * 续级信息
     */
    private MemberLevelSoaModel DegradeInfo;
    /**
     * 最近周期（前12个月）已累积定级积分数
     */
    private Integer RecentCycleMiles;
    /**
     * 最近周期（前12个月）已累积定级航段数
     */
    private Integer RecentCycleSegments;

}
