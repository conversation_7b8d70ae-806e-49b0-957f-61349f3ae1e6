package com.juneyaoair.thirdentity.member.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName MarketActivityRuleQueryRequest
 * @Description 营销活动规则查询
 * <AUTHOR>
 * @Date 2019/6/28 13:45
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MarketActivityRuleQueryRequest {

    /**
     * 规则Id（支持多个规则Id）
     */
    private List<String> RuleIds;
}
