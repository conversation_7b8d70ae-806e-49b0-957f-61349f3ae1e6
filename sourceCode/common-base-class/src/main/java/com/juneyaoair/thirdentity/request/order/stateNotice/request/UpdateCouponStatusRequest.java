package com.juneyaoair.thirdentity.request.order.stateNotice.request;

import com.juneyaoair.thirdentity.request.order.stateNotice.CommentCompleteNotice;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2021-3-24 19:07
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class UpdateCouponStatusRequest {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private CommentCompleteNotice Request;
}
