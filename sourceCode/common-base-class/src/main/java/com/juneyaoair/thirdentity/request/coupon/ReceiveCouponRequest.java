package com.juneyaoair.thirdentity.request.coupon;



/**
 * Created by <PERSON><PERSON> on 2016-07-19.
 */
public class ReceiveCouponRequest {
    private String Version; // 接口版本号10
    private String ChannelCode; // 渠道用户号B2C,CC等
    private String UserNo; // 渠道工作人员号分配给渠道用户的工作人员号
    private String FfpId;
    private String FfpCardNo;
    private String ActivityNo; //活动号

    public ReceiveCouponRequest() {
        super();
    }

    public ReceiveCouponRequest(String version, String channelCode, String userNo, String ffpId, String ffpCardNo, String activityNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
        FfpId = ffpId;
        FfpCardNo = ffpCardNo;
        ActivityNo = activityNo;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getFfpId() {
        return FfpId;
    }

    public void setFfpId(String ffpId) {
        FfpId = ffpId;
    }

    public String getFfpCardNo() {
        return FfpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        FfpCardNo = ffpCardNo;
    }

    public String getActivityNo() {
        return ActivityNo;
    }

    public void setActivityNo(String activityNo) {
        ActivityNo = activityNo;
    }
}
