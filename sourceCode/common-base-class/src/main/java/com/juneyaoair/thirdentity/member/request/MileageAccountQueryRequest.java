package com.juneyaoair.thirdentity.member.request;

import com.juneyaoair.baseclass.common.base.UserInfoMust;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName MileageAccountQueryRequest
 * @Description 积分账户查询请求参数
 * <AUTHOR>
 * @Date 2020/3/6 14:33
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MileageAccountQueryRequest{

    /**
     * 会员卡号
     */
    private String MemberCardNo;

    /**
     * 查询开始时间
     * yyyyMMdd
     * 默认为一年之前
     */
    private String StartDate;

    /**
     * 查询结束时间
     * yyyyMMdd
     * 默认为当天
     */
    private String EndDate;

    /**
     * 积分最近MonthNum个月到期账单（包含当前月）
     */
    private Integer MonthNum;

    /**
     *
     * 请求项（TotalBill：总账信息；PeriodBill：时间段账单信息；ExpireBill：积分失效账单；LevelChange：升降级信息）
     */
    private String[] RequestItems;

}
