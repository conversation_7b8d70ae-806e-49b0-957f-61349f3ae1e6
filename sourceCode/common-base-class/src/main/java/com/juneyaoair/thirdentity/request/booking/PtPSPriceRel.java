package com.juneyaoair.thirdentity.request.booking;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "PSPriceRel")
@XmlAccessorType(XmlAccessType.FIELD)
public class PtPSPriceRel {
	
	private int PassengerNO; //乘客序号	
	private int SegNO; //旅行顺序	相同乘客类型的同一航段的运价序号必须是相同
	private int PriceNO; //运价ID	
	private boolean IsSelectedFare; //选择标识	固定为True,一名乘客一个航段只可以与一条运价对应
	public PtPSPriceRel() {}
	public PtPSPriceRel(int PassengerNO, int SegNO, int PriceNO, boolean IsSelectedFare) {
		this.PassengerNO = PassengerNO;
		this.SegNO = SegNO;
		this.PriceNO = PriceNO;
		this.IsSelectedFare = IsSelectedFare;
	}
	
	public int getPassengerNO() {
		return PassengerNO;
	}
	public void setPassengerNO(int passengerNO) {
		PassengerNO = passengerNO;
	}
	public int getSegNO() {
		return SegNO;
	}
	public void setSegNO(int segNO) {
		SegNO = segNO;
	}
	public int getPriceNO() {
		return PriceNO;
	}
	public void setPriceNO(int priceNO) {
		PriceNO = priceNO;
	}
	public boolean getIsSelectedFare() {
		return IsSelectedFare;
	}
	public void setIsSelectedFare(boolean isSelectedFare) {
		IsSelectedFare = isSelectedFare;
	}
}