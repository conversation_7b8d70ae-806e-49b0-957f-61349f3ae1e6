package com.juneyaoair.thirdentity.request.av;

import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * 航班运价查询
 * 
 * */
@XmlRootElement(name = "QueryFareRequest")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"Version","ChannelCode","UserNo","RouteType","CurrencyCode","TicketingDate","PriceProductTypeList","SegCondList","Direct"})
public class QueryFareRequest {
	
	private	String	Version;	//	接口版本号	10
	private	String	ChannelCode;	//	渠道用户号	B2C,CC等
	private	String	UserNo;	//	渠道工作人员号	分配给渠道用户的工作人员号
	private	String	RouteType;	//	航程类型	单程：OW；往返：RT（RT时请注意飞行方向）
	private	String	CurrencyCode;	//	币种代码	CNY人民币
	private	String	TicketingDate;	//	出票时间，可以理解为：航班预订时间，即当前时间。	yyyy-MM-dd hh:mi
	private	List<String>	PriceProductTypeList;	//	运价产品类型	1 - 公布,2 - 私有,3 - 多程惠达，4 - 中转联程,为空时表示查询所有,只需要查询某项或某几项时给出需要的产品代码
	private	List<Segment>	SegCondList;	//	航段条件数组	
	private	String	Direct;	//	中转方案	"D － 只查询直达航班和运价，T － 没有直达方案时，给出中转方案运价.S － 没有直达方案时,只给出中转航程，不查询航班可利用座位和运价"
	private 	String LangCode;

	public QueryFareRequest(){}
	public QueryFareRequest(String	Version,String	ChannelCode, String	UserNo,String	RouteType,String	CurrencyCode,String	TicketingDate,List<String>	PriceProductTypeList,
			List<Segment> SegCondList,	String	Direct, String LangCode) {
		this.Version = Version;
		this.ChannelCode = ChannelCode;
		this.UserNo = UserNo;
		this.RouteType = RouteType;
		this.CurrencyCode = CurrencyCode;
		this.TicketingDate = TicketingDate;
		this.PriceProductTypeList = PriceProductTypeList;
		this.SegCondList = SegCondList;
		this.Direct = Direct;
		this.LangCode = LangCode;
	}
	
	public String getVersion() {
		return Version;
	}
	public void setVersion(String version) {
		Version = version;
	}
	public String getChannelCode() {
		return ChannelCode;
	}
	public void setChannelCode(String channelCode) {
		ChannelCode = channelCode;
	}
	public String getUserNo() {
		return UserNo;
	}
	public void setUserNo(String userNo) {
		UserNo = userNo;
	}
	public String getRouteType() {
		return RouteType;
	}
	public void setRouteType(String routeType) {
		RouteType = routeType;
	}
	public String getCurrencyCode() {
		return CurrencyCode;
	}
	public void setCurrencyCode(String currencyCode) {
		CurrencyCode = currencyCode;
	}
	public String getTicketingDate() {
		return TicketingDate;
	}
	public void setTicketingDate(String ticketingDate) {
		TicketingDate = ticketingDate;
	}
	
	public List<String> getPriceProductTypeList() {
		return PriceProductTypeList;
	}

	public void setPriceProductTypeList(List<String> priceProductTypeList) {
		PriceProductTypeList = priceProductTypeList;
	}

	public List<Segment> getSegCondList() {
		return SegCondList;
	}

	public void setSegCondList(List<Segment> segCondList) {
		SegCondList = segCondList;
	}

	public String getDirect() {
		return Direct;
	}
	public void setDirect(String direct) {
		Direct = direct;
	}

	public String getLangCode() {
		return LangCode;
	}

	public void setLangCode(String langCode) {
		LangCode = langCode;
	}

	@Override
	public String toString() {
		return "QueryFareRequest [Version=" + Version + ", ChannelCode="
				+ ChannelCode + ", UserNo=" + UserNo + ", RouteType="
				+ RouteType + ", CurrencyCode=" + CurrencyCode
				+ ", TicketingDate=" + TicketingDate
				+ ", PriceProductTypeList=" + PriceProductTypeList
				+ ", SegCondList=" + SegCondList + ", Direct=" + Direct + "]";
	}
	

	

}
