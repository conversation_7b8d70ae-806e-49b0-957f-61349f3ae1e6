package com.juneyaoair.thirdentity.request.xinyongfei;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by yaocf on 2017/6/14.
 */
@Data
public class PtCreditLimitReq {
    private String GatewayNo;
    private String ChannelNo;
    private String ExtraParam;
    private String Mobile;
    private String Name;
    private String IdCardNumber;
    private String UtmSource;

    public PtCreditLimitReq(String channelNo, String extraParam) {
        ChannelNo = channelNo;
        ExtraParam = extraParam;
    }

    public PtCreditLimitReq(String channelNo, String mobile, String name, String idCardNumber, String extraParam){
        ChannelNo=channelNo;
        Mobile=mobile;
        Name=name;
        IdCardNumber=idCardNumber;
        ExtraParam=extraParam;
    }

    public Map<String,String> getParaMap(){
        Map<String,String> parametersMap = new HashMap<String,String>();
        //1网关号
        parametersMap.put("GatewayNo", this.getGatewayNo());
        //2渠道用户
        parametersMap.put("ChannelNo", this.getChannelNo());
        //3自定义参数
        parametersMap.put("ExtraParam", this.getExtraParam());
        //4手机号
        parametersMap.put("Mobile", this.getMobile());
        //5姓名
        parametersMap.put("Name", this.getName());
        //6身份证号
        parametersMap.put("IdCardNumber", this.getIdCardNumber());
        //7航司来源
        parametersMap.put("UtmSource", this.getUtmSource());
        return parametersMap;
    }
}
