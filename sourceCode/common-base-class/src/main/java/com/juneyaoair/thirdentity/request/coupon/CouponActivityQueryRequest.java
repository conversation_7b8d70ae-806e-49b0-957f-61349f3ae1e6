package com.juneyaoair.thirdentity.request.coupon;

/**
 * Created by <PERSON><PERSON> on 2016-07-18.
 */
public class CouponActivityQueryRequest {
    private String Version; // 接口版本号10
    private String ChannelCode; // 渠道用户号B2C,CC等
    private String UserNo; // 渠道工作人员号分配给渠道用户的工作人员号
    private String FfpId;//会员ID
    private String FfpCardNo;//会员卡号
    private Integer Sale;   //0：全部 1：仅非可售 2：仅可售


    public CouponActivityQueryRequest() {
    }

    public CouponActivityQueryRequest(String version, String channelCode, String userNo) {
        Version = version;
        ChannelCode = channelCode;
        UserNo = userNo;
    }

    public String getVersion() {
        return Version;
    }

    public void setVersion(String version) {
        Version = version;
    }

    public String getChannelCode() {
        return ChannelCode;
    }

    public void setChannelCode(String channelCode) {
        ChannelCode = channelCode;
    }

    public String getUserNo() {
        return UserNo;
    }

    public void setUserNo(String userNo) {
        UserNo = userNo;
    }

    public String getFfpId() {
        return FfpId;
    }

    public void setFfpId(String ffpId) {
        FfpId = ffpId;
    }

    public String getFfpCardNo() {
        return FfpCardNo;
    }

    public void setFfpCardNo(String ffpCardNo) {
        FfpCardNo = ffpCardNo;
    }

    public int getSale() {
        return Sale;
    }

    public void setSale(int sale) {
        Sale = sale;
    }
}
