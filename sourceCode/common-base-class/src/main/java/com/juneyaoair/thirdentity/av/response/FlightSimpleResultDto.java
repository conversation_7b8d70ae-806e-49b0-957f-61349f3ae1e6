package com.juneyaoair.thirdentity.av.response;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @create 2020-06-23 18:36
 */
@Data
public class FlightSimpleResultDto {

    /**
     * 航班信息
     */
    @SerializedName("FlightInfoCombApiDtos")
    private List<FlightInfoCombApi> flightInfoCombApiDtos;

    /**
     * 国内国际标识
     */
    @SerializedName("InterFlag")
    private String interFlag;

    /**
     * 运价来源
     */
    @SerializedName("FareSource")
    private String fareSource;

}
