package com.juneyaoair.thirdentity.change.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/29  15:20.
 */
@Data
public class ChangeSegmentInfo {
    private int SegmentID; //航段ID保存在统一订单数据库中的ID

    private int SegNO; //旅行顺序第一段为从0开始，第二段为1，依次增加

    private String FlightDirection; //飞行方向去程为G,回程为B

    private String FlightNo; //航班号

    private String DepDateTime; //航班起飞时间yyyy-MM-dd HH:mm

    private String ArrDateTime; //航班到达时间yyyy-MM-dd HH:mm

    private String DepCity; //起飞城市三字码

    private String ArrCity; //到达城市三字码

    private String DepAirport; //起飞机场三字码

    private String ArrAirport; //到达机场三字码

    private String Cabin; //舱位

    private String CabinClass; //舱位等级

    private boolean IsCodeShare; //是否共享航班

    private String CarrierFlightNo; //承运航班号

    private String MealCode; //餐食代码

    private boolean IsSeatedOnPlane; //是否可以机上订位

    private String PlaneStyle; //机型

    private String DepTerm; //起飞航站楼

    private String ArrTerm; //到达航站楼

    private String StopNumber; //经停次数

    private List<ChangeFeeInfo> ChangeFeeList;
    /**
     * 是否可改期
     */
    private boolean IsChange;
}
