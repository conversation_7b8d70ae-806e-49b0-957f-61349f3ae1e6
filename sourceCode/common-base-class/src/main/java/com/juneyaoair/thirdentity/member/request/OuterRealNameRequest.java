package com.juneyaoair.thirdentity.member.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> by jiang<PERSON><PERSON>
 * @date 2019/3/6 10:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OuterRealNameRequest {
    /**
     * Code:0 Name:UNKNOW Description:未知
     • Code:1 Name:ZhiFuBao Description:支付宝
     • Code:2 Name:Unionpay Description:银联
     • Code:3 Name:Photo Description:证件照
     • Code:4 Name:Face Description:人脸识别
     */
    private String VerifyChannel;

    private String Referrer; // 2021-02-02 小程序 实名认证来源
}
