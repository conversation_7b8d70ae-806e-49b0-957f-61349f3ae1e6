package com.juneyaoair.thirdentity.common.invoice.response;

import com.juneyaoair.thirdentity.response.PtBassResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName GeneralInvoiceResponse
 * <AUTHOR>
 * @Description
 * @Date 2020-11-12 16:09
 **/
@NoArgsConstructor
@Data
public class ModifyGeneralInvoiceResponse extends PtBassResponse {

    /**
     * UserNo : 10001
     * Data : {"GeneralInvoiceId":100004,"Payable":"吉祥航空有限公司"}
     */

    private String UserNo;
    private GeneralInvoiceInfoDto Data;
}
