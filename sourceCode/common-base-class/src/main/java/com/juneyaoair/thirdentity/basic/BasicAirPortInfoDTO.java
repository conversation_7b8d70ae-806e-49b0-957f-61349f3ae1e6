package com.juneyaoair.thirdentity.basic;

import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2020/3/9  14:22.
 */
@Data
public class BasicAirPortInfoDTO {
    private String airportCode;

    private String airportName;

    private String airportEName;

    private String cityCode;

    private String nameAbb;

    private String englishNameAbb;

    private String pinyinAbb;

    private String baiduMapPoint;

    private String airportPinyin;

    private String website;

    private String terminalposition;

    private String checkincounter;

    private String firstclasscheckincounter;

    private String ticketcounter;

    private String checkinbegintime;

    private String checkinendtime;

    private String viproom;

    private String iTerminalposition;

    private String iCheckincounter;

    private String iFirstclasscheckincounter;

    private String iViproom;

    private String iTicketcounter;

    private String airportKoName;

    private String airportJpName;

    private String airportThName;

    private String airportTcName;

    //来自城市表
    private String cityName;
    private String cityEName;
    private String isInternational;

    private SimplifyCityDTO cityInfo;
}
