package com.juneyaoair.thirdentity.ticket.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 证件查询客票列表请求参数
 * @date 2023/8/25 16:10
 */
@ApiModel(value = "PtTicketDigestRequestDto",description = "证件查询客票列表请求")
@Data
@Builder
public class PtTicketDigestRequestDto {
    @ApiModelProperty(value = "接口版本号",notes = "10.0")
    private String Version;
    @ApiModelProperty(value = "渠道用户号")
    private String ChannelCode;
    @ApiModelProperty(value = "证件类型")
    private String CertType;
    @ApiModelProperty(value = "证件号")
    private String CertNo;
    @ApiModelProperty(value = "旅客姓名")
    private String PassengerName;
    @ApiModelProperty(value = "指定航段状态",allowableValues = "OPEN,OPEN FOR USE,FLOWN,EXCH,REFU,CHECKED IN,VOID")
    private List<String> SegmentStatusList;
}
