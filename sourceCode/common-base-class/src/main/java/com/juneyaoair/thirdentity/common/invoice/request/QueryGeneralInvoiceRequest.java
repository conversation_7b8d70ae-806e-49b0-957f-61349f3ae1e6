package com.juneyaoair.thirdentity.common.invoice.request;

import com.juneyaoair.thirdentity.request.PtBaseRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName GeneralInvoiceRequest
 * <AUTHOR>
 * @Description
 * @Date 2020-11-12 15:36
 **/
@NoArgsConstructor
@Data
public class QueryGeneralInvoiceRequest extends PtBaseRequest {

    /**
     * PageNo : 1
     * PageSize : 10
     * ChannelCustomerNo : 3510604
     * ChannelCustomerType : CRM
     */

    private int PageNo;
    private int PageSize;
    private String ChannelCustomerNo;
    private String ChannelCustomerType;

    public QueryGeneralInvoiceRequest(String version, String channelCode, String userNo){
        super(version,channelCode,userNo);
    }
}
