package com.juneyaoair.thirdentity.common.invoice.response;

import com.juneyaoair.thirdentity.common.invoice.GeneralInvoiceInfoDTO;
import com.juneyaoair.thirdentity.response.PtBassResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName GeneralInvoiceResponse
 * <AUTHOR>
 * @Description
 * @Date 2020-11-12 16:09
 **/
@NoArgsConstructor
@Data
public class QueryGeneralInvoiceResponse extends PtBassResponse {


    /**
     * Total : 4
     * Data : [{"GeneralInvoiceId":100001,"PayableType":"1","Payable":"上海吉祥航空有限公司","TaxNumber":"9784654645DKSHF234","Bank":"中国银行上海支行","BankAccount":"6220225646465797634","CompanyAddress":"闵行区123号","CompanyPhone":"***********","CompanyMail":"<EMAIL>"},{"GeneralInvoiceId":100002,"PayableType":"1","Payable":"吉祥航空有限公司","TaxNumber":"9784654645DKSHF234","Bank":"中国银行上海支行","BankAccount":"6220225646465797634","CompanyAddress":"闵行区123号","CompanyPhone":"021-********"},{"GeneralInvoiceId":100003,"PayableType":"1","Payable":"阿里巴巴集团有限公司","TaxNumber":"9784654645DKSHF234","Bank":"中国银行杭州支行","BankAccount":"6220225646465797634","CompanyAddress":"浙江省杭州市西湖街1688号","CompanyPhone":"021-********"},{"GeneralInvoiceId":100004,"PayableType":"1","Payable":"中国石化集团","TaxNumber":"9784654645DKSHF234","Bank":"中国银行杭州支行","BankAccount":"6220225646465797634","CompanyAddress":"浙江省杭州市西湖街1688号","CompanyPhone":"021-********"}]
     */

    private int Total;
    private List<GeneralInvoiceInfoDTO> Data;
}
