package com.juneyaoair.thirdentity.av.response;

import com.juneyaoair.thirdentity.av.comm.Fare;
import com.juneyaoair.thirdentity.av.comm.FareTaxInfo;
import com.juneyaoair.thirdentity.av.comm.V2FlightInfo;
import lombok.Data;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description 新的运价返回结果V20
 * @date 2018/12/5  19:34.
 */
@Data
public class PtQueryFlightFareResponse {
    private String Version;
    private String ChannelCode;
    private String UserNo;
    private String RandCode;
    private String RouteType;
    private String CurrencyCode;
    private String LangCode;
    //旅行类别: D-国内,I-国际
    private String InterFlag;
    //舱位顺序  F/A/Y/...
    private String CabinSequence;
    //航段航班信息列表
    private List<V2FlightInfo> FlightInfoList;
    //联程航段航班信息列表
    private List<FlightInfoCombApi> FlightInfoCombList;
    private List<FareTaxInfo> TaxInfoList;
    private HashMap<String,Fare> FareDic;
    private String FareSource;//运价类型
    /**
     * 航班结果来源 AIROC-航鹏
     */
    private String FareCallSource;
    private String ResultCode;
    private String ErrorInfo;
    private String UuId;
}
