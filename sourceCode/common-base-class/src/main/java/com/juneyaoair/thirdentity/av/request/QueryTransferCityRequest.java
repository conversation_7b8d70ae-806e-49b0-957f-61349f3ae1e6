package com.juneyaoair.thirdentity.av.request;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class QueryTransferCityRequest {

//        {
//            "Version": "10",
//                "ChannelCode": "MOBILE",
//                "DepCity": "HRB",
//                "ArrCity": "SYX",
//                "FlightDate": "2024-10-29",
//                "RandCode": "283dc348-0f0c-4c1c-b577-54154ab1742b"
//        }

    @SerializedName("Version")
    private String version;

    @SerializedName("ChannelCode")
    private String channelCode;

    @SerializedName("DepCity")
    private String depCity;

    @SerializedName("ArrCity")
    private String arrCity;

    @SerializedName("FlightDate")
    private String flightDate;

    @SerializedName("RandCode")
    private String randCode;

}


