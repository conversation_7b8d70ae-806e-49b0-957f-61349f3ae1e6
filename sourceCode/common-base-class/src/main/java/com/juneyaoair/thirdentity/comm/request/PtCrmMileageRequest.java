package com.juneyaoair.thirdentity.comm.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName PtCrmMileageRequest
 * @Description  java 微服务 CRM接口通用请求参数
 * <AUTHOR>
 * @Date 2020/3/17 15:43
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PtCrmMileageRequest<T> {
    /**
     * 业务数据
     */
    private T Data;
    /**
     * 渠道名称
     */
    private String Channel;
    /**
     * 渠道密码
     */
    private String ChannelPwd;
    /**
     * 请求IP
     */
    private String ClientIP;
    /**
     * 版本号
     */
    private String Version;
    /**
     * 业务随机码
     * SgdsR2fi1，用于错误排查或业务追踪
     */
    private String RandomCode;
    /**
     * 操作者  可不传（常旅客后台除外）
     */
    private String OperatorUid;
    /**
     * 时间戳（默认为系统当前时间）
     */
    private String Timestamp;

    /**
     * 加密数据
     */
    private String Encryptor;
    /**
     * 签名数据
     */
    private String Signature;

    public PtCrmMileageRequest(String channel, String channelPwd, String version) {
        Channel = channel;
        ChannelPwd = channelPwd;
        Version = version;
    }
}
