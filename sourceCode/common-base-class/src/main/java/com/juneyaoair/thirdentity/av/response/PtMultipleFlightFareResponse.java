package com.juneyaoair.thirdentity.av.response;

import com.google.gson.annotations.SerializedName;
import com.juneyaoair.thirdentity.av.comm.Fare;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 统一订单多程运价查询响应参数
 * <AUTHOR>
 * @Description
 * @create 2020-06-23 9:57
 */
@Data
public class PtMultipleFlightFareResponse {
    @SerializedName("Version")
    private String version;
    @SerializedName("ChannelCode")
    private String channelCode;
    @SerializedName("RandCode")
    private String randCode;
    @SerializedName("RouteType")
    private String routeType;
    @SerializedName("CurrencyCode")
    private String currencyCode;
    @SerializedName("LangCode")
    private String langCode;
    //舱位顺序  F/A/Y/...
    @SerializedName("CabinSequence")
    private String cabinSequence;
    @SerializedName("FareDic")
    private HashMap<String, Fare> fareDic;
    @SerializedName("FareSource")
    private String fareSource;//运价类型
    @SerializedName("ResultCode")
    private String resultCode;
    @SerializedName("ErrorInfo")
    private String errorInfo;
    @SerializedName("MultiFlightInfoCombMap")
    private Map<Integer,FlightSimpleResultDto> multiFlightInfoCombMap;
}
