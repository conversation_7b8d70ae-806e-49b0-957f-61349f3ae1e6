package com.juneyaoair.exception;

/**
 * @ClassName ExpectedException
 * @Description 系统运行过程中抛出的由可预知原因导致的异常
 * <AUTHOR>
 * @Date 2019/5/6 8:45
 **/
public class ExpectableException extends RuntimeException {

    public ExpectableException() {super();}

    public ExpectableException(String message) {super(message);}

    public ExpectableException(Throwable cause) {
        super(cause);
    }

    public ExpectableException(String message, Throwable cause) {
        super(message, cause);
    }

    protected ExpectableException(String message, Throwable cause,
                               boolean enableSuppression,
                               boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
