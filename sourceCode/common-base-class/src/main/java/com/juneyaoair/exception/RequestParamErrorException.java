package com.juneyaoair.exception;

/**
 * @ClassName RequestParamErrorException
 * @Description 请求参数校验不通过抛出的异常
 * <AUTHOR>
 * @Date 2019/5/8 13:10
 **/
public class RequestParamErrorException extends ExpectableException {

    public RequestParamErrorException() {super();}

    public RequestParamErrorException(String message) {super(message);}

    public RequestParamErrorException(Throwable cause) {
        super(cause);
    }

    public RequestParamErrorException(String message, Throwable cause) {
        super(message, cause);
    }

    protected RequestParamErrorException(String message, Throwable cause,
                                       boolean enableSuppression,
                                       boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }


}
