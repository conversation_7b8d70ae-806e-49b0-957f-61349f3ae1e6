package com.juneyaoair.exception;

/**
 * @Author: caolei
 * @Description: RetryServiceException 根据需要自行处理是否进行异常捕获重试
 * @Date: 2022/01/27 16:18
 * @Modified by:
 */
public class RetryServiceException extends RuntimeException {
    /** 返回编码 */
    private final String resultCode;
    /** 返回结果描述 */
    private final String errorMsg;
    /** 返回数据 */
    private Object data;

    /**
     * CommonException异常
     * @param resultCode    返回编码
     * @param errorMsg      返回描述
     */
    public RetryServiceException(String resultCode, String errorMsg) {
        super(errorMsg);
        this.resultCode = resultCode;
        this.errorMsg = errorMsg;
    }

    /**
     * CommonException异常
     * @param resultCode    返回编码
     * @param errorMsg      返回描述
     * @param data          返回数据
     */
    public RetryServiceException(String resultCode, String errorMsg, Object data) {
        super(errorMsg);
        this.resultCode = resultCode;
        this.errorMsg = errorMsg;
        this.data = data;
    }

    public String getResultCode() {
        return resultCode;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public Object getData() {
        return data;
    }
}