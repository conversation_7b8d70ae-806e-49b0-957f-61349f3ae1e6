package com.juneyaoair.exception;

/**
 * <AUTHOR>
 * @description 网络异常类
 * @date 2020/6/30  10:22.
 */
public class NetworkException extends ExpectableException{
    public NetworkException() {super();}

    public NetworkException(String message) {super(message);}

    public NetworkException(Throwable cause) {
        super(cause);
    }

    public NetworkException(String message, Throwable cause) {
        super(message, cause);
    }

    protected NetworkException(String message, Throwable cause,
                               boolean enableSuppression,
                               boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
